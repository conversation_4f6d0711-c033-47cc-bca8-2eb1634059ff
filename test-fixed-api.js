// Test the fixed API
async function testFixedAPI() {
  try {
    console.log('🧪 Testing Fixed API on localhost:3000...\n');
    
    // Test 1: Check server health
    console.log('1. Testing server health...');
    const healthResponse = await fetch('http://localhost:3000/');
    console.log('✅ Server responding:', healthResponse.ok ? 'Yes' : 'No');
    
    // Test 2: Test CORS by making a cross-origin request
    console.log('\n2. Testing CORS...');
    const corsResponse = await fetch('http://localhost:3000/api/news');
    console.log('✅ CORS working:', corsResponse.ok ? 'Yes' : 'No');
    
    // Test 3: Register a new user
    console.log('\n3. Testing user registration...');
    const username = 'testuser' + Date.now();
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password: 'testpass123' })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ Registration:', registerData.message);
    
    if (registerData.token) {
      console.log('🔑 Token received: Yes');
      
      // Test 4: Create a link
      console.log('\n4. Testing link creation...');
      const linkResponse = await fetch('http://localhost:3000/api/links/shorten', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${registerData.token}`
        },
        body: JSON.stringify({ originalUrl: 'https://example.com' })
      });
      
      const linkData = await linkResponse.json();
      console.log('✅ Link created:', linkData.shortCode);
      console.log('🔗 Short URL:', linkData.shortUrl);
      
      // Test 5: Test the API endpoint for getting original URL (not redirect)
      console.log('\n5. Testing original URL API...');
      const originalResponse = await fetch(`http://localhost:3000/api/links/original/${linkData.shortCode}`);
      const originalData = await originalResponse.json();
      console.log('✅ Original URL API:', originalData.originalUrl);
      
      console.log('\n🎉 All API tests passed! System is working correctly!');
      console.log('\n📱 You can now test in browser:');
      console.log('   • Registration/Login: http://localhost:3000');
      console.log('   • Dashboard: After login');
      console.log('   • Link redirect: http://localhost:3000/s/' + linkData.shortCode);
      
    } else {
      console.log('❌ No token received');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFixedAPI();
