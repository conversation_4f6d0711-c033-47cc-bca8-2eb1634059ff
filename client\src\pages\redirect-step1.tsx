import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Clock, ArrowDown, Gamepad, Trophy, Target } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export default function RedirectStep1() {
  const [, setLocation] = useLocation();
  const [countdown, setCountdown] = useState(20);
  const [showContinue, setShowContinue] = useState(false);
  const [secondCountdown, setSecondCountdown] = useState(10);
  const [showSecondContinue, setShowSecondContinue] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);

  // Get short code from URL
  const shortCode = new URLSearchParams(window.location.search).get('code') || '';

  useEffect(() => {
    // Load Monetag MultiTag script
    const multitagScript = document.createElement('script');
    multitagScript.src = 'https://fpyf8.com/88/tag.min.js';
    multitagScript.setAttribute('data-zone', '156349');
    multitagScript.async = true;
    multitagScript.setAttribute('data-cfasync', 'false');
    document.body.appendChild(multitagScript);

    // Load native banner (interstitial) script
    const interstitialScript = document.createElement('script');
    interstitialScript.text = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))`;
    document.body.appendChild(interstitialScript);

    return () => {
      // Cleanup scripts on unmount
      if (document.body.contains(multitagScript)) {
        document.body.removeChild(multitagScript);
      }
      if (document.body.contains(interstitialScript)) {
        document.body.removeChild(interstitialScript);
      }
    };
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setShowContinue(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      if (scrollPosition > windowHeight * 0.5) {
        setHasScrolled(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleFirstContinue = () => {
    const timer = setInterval(() => {
      setSecondCountdown(prev => {
        if (prev <= 1) {
          setShowSecondContinue(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setTimeout(() => clearInterval(timer), 10000);
  };

  const handleSecondContinue = () => {
    if (hasScrolled) {
      // Open new tab for step 2
      const newTab = window.open(`/redirect-step2?code=${shortCode}`, '_blank');
      if (newTab) {
        newTab.focus();
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600">
      
      {/* Navigation */}
      <nav className="glassmorphism relative z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <Gamepad className="text-white text-2xl" />
              <span className="text-white text-xl font-bold">Gaming News Hub</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Countdown Banner */}
      {countdown > 0 && (
        <div className="bg-red-600 text-white text-center py-3">
          <div className="flex items-center justify-center space-x-2">
            <Clock className="h-5 w-5" />
            <span className="font-bold">Please wait {countdown} seconds...</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="glassmorphism rounded-xl p-8 mb-8 text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🎮 Latest Gaming News & Updates
          </h1>
          <p className="text-gray-300 text-lg">
            Stay ahead in the gaming world with our exclusive content
          </p>
        </div>

        {/* Monetag Ad Zone */}
        <div className="glassmorphism rounded-xl p-6 mb-8 text-center">
          <div id="monetag-ad-zone-1" className="min-h-[200px] flex items-center justify-center">
            {/* MultiTag ads will automatically appear here */}
            <div className="text-gray-400 text-sm">Loading content...</div>
          </div>
        </div>

        {/* Gaming News Articles */}
        <div className="space-y-8">
          <article className="glassmorphism rounded-xl p-8">
            <div className="flex items-center space-x-2 mb-4">
              <Trophy className="text-yellow-400 h-6 w-6" />
              <span className="text-pink-400 font-semibold">Breaking News</span>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              Major Gaming Tournament Breaks All-Time Viewership Records
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Tournament" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              The latest gaming championship has shattered all previous viewership records, drawing over 50 million concurrent viewers worldwide. The tournament, featuring the biggest names in competitive gaming, showcased incredible skill and determination across multiple game titles.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Professional esports has evolved dramatically over the past decade, transforming from niche competitions to mainstream entertainment events that rival traditional sports in terms of audience engagement and prize pools.
            </p>
          </article>

          {/* Monetag Ad Zone */}
          <div className="glassmorphism rounded-xl p-6 text-center">
            <div id="monetag-ad-zone-2" className="min-h-[200px] flex items-center justify-center">
              {/* MultiTag ads will automatically appear here */}
              <div className="text-gray-400 text-sm">Loading content...</div>
            </div>
          </div>

          <article className="glassmorphism rounded-xl p-8">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="text-blue-400 h-6 w-6" />
              <span className="text-blue-400 font-semibold">Industry Update</span>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              Next-Generation Gaming Hardware Revolutionizes Performance
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Hardware" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              The gaming industry continues to push the boundaries of what's possible with cutting-edge hardware releases that deliver unprecedented performance levels. From advanced graphics cards to lightning-fast processors, the latest technology is reshaping the gaming landscape.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              These technological advances are not just about raw power - they're enabling entirely new gaming experiences with ray tracing, high refresh rates, and immersive virtual reality that brings players closer to the action than ever before.
            </p>
          </article>

          {/* Continue Button Section */}
          {showContinue && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <Button 
                onClick={handleFirstContinue}
                className="bg-pink-500 hover:bg-pink-600 text-white font-semibold py-3 px-8 rounded-lg text-lg"
              >
                Continue Reading
              </Button>
            </div>
          )}

          {/* Second Countdown and Continue */}
          {secondCountdown > 0 && showContinue && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Clock className="h-5 w-5 text-pink-400" />
                <span className="text-white font-bold">Please wait {secondCountdown} more seconds...</span>
              </div>
              <div className="animate-bounce">
                <ArrowDown className="h-8 w-8 text-pink-400 mx-auto" />
              </div>
              <p className="text-gray-300 mt-2">Scroll down to continue</p>
            </div>
          )}

          {showSecondContinue && hasScrolled && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <Button 
                onClick={handleSecondContinue}
                className="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg text-lg"
              >
                Continue to Next Step
              </Button>
            </div>
          )}

          {/* More Content */}
          <article className="glassmorphism rounded-xl p-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Gaming Community Highlights
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Community" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              The gaming community continues to amaze with incredible creativity, from stunning fan art to innovative game modifications that extend the life of beloved titles. Community-driven content has become an integral part of the gaming ecosystem.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed">
              Independent developers are also making their mark, creating unique gaming experiences that challenge conventional design and storytelling approaches, proving that innovation can come from anywhere in the gaming world.
            </p>
          </article>

          {/* Ad Space */}
          <div className="glassmorphism rounded-xl p-6 text-center">
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8">
              <p className="text-gray-300">Advertisement Space</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}