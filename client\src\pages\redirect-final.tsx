import { useState, useEffect } from "react";
import { Clock, ExternalLink, Shield, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { getApiUrl } from "../config";

export default function RedirectFinal() {
  const [countdown, setCountdown] = useState(20);
  const [showGetLink, setShowGetLink] = useState(false);
  const [finalCountdown, setFinalCountdown] = useState(5);
  const [showFinalLink, setShowFinalLink] = useState(false);
  const [originalUrl, setOriginalUrl] = useState('');

  // Get short code from URL
  const shortCode = new URLSearchParams(window.location.search).get('code') || '';

  useEffect(() => {
    // Load Monetag MultiTag script
    const multitagScript = document.createElement('script');
    multitagScript.src = 'https://fpyf8.com/88/tag.min.js';
    multitagScript.setAttribute('data-zone', '156349');
    multitagScript.async = true;
    multitagScript.setAttribute('data-cfasync', 'false');
    document.body.appendChild(multitagScript);

    // Load native banner (interstitial) script
    const interstitialScript = document.createElement('script');
    interstitialScript.text = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))`;
    document.body.appendChild(interstitialScript);

    return () => {
      // Cleanup scripts on unmount
      if (document.body.contains(multitagScript)) {
        document.body.removeChild(multitagScript);
      }
      if (document.body.contains(interstitialScript)) {
        document.body.removeChild(interstitialScript);
      }
    };
  }, []);

  useEffect(() => {
    // Fetch the original link data
    const fetchOriginalLink = async () => {
      try {
        const response = await fetch(getApiUrl(`/api/links/original/${shortCode}`));
        if (response.ok) {
          const data = await response.json();
          setOriginalUrl(data.originalUrl);
        }
      } catch (error) {
        console.error('Error fetching original link:', error);
      }
    };

    fetchOriginalLink();

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setShowGetLink(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [shortCode]);

  const handleGetLink = () => {
    const timer = setInterval(() => {
      setFinalCountdown(prev => {
        if (prev <= 1) {
          setShowFinalLink(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setTimeout(() => clearInterval(timer), 5000);
  };

  const handleFinalRedirect = async () => {
    try {
      // Track the click using the API endpoint (not the redirect endpoint)
      await fetch(getApiUrl(`/api/links/original/${shortCode}`));
      // Redirect to original URL
      window.location.href = originalUrl;
    } catch (error) {
      // Fallback redirect
      window.location.href = originalUrl;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600">
      
      {/* Navigation */}
      <nav className="glassmorphism relative z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <Shield className="text-white text-2xl" />
              <span className="text-white text-xl font-bold">Secure Link Gateway</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="max-w-3xl mx-auto px-4 py-16">
        <div className="glassmorphism rounded-xl p-12 text-center">
          {/* Security Header */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Shield className="h-12 w-12 text-green-400" />
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              🔒 Secure Link Verification
            </h1>
            <p className="text-gray-300 text-lg">
              Your link is being processed through our security system
            </p>
          </div>

          {/* Ad Space */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8 mb-8">
            <p className="text-gray-300">Advertisement Space</p>
          </div>

          {/* Countdown Section */}
          {countdown > 0 && (
            <div className="mb-8">
              <div className="bg-blue-600 bg-opacity-30 border border-blue-500 rounded-lg p-6 mb-6">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <Clock className="h-8 w-8 text-blue-400 animate-pulse" />
                  <span className="text-3xl font-bold text-white">{countdown}</span>
                </div>
                <p className="text-gray-300 text-lg">
                  Verifying link security and authenticity...
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <CheckCircle className="h-6 w-6 text-green-400 mx-auto mb-2" />
                  <p className="text-white font-semibold">Malware Scan</p>
                  <p className="text-gray-300">Complete</p>
                </div>
                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <CheckCircle className="h-6 w-6 text-green-400 mx-auto mb-2" />
                  <p className="text-white font-semibold">Spam Check</p>
                  <p className="text-gray-300">Verified</p>
                </div>
                <div className="bg-white bg-opacity-10 rounded-lg p-4">
                  <Clock className="h-6 w-6 text-yellow-400 mx-auto mb-2 animate-pulse" />
                  <p className="text-white font-semibold">Final Check</p>
                  <p className="text-gray-300">Processing...</p>
                </div>
              </div>
            </div>
          )}

          {/* Get Link Button */}
          {showGetLink && !showFinalLink && (
            <div className="mb-8">
              <div className="bg-green-600 bg-opacity-30 border border-green-500 rounded-lg p-6 mb-6">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <p className="text-green-400 text-xl font-bold mb-2">
                  ✅ Security Check Complete
                </p>
                <p className="text-gray-300">
                  Your link has been verified and is safe to access
                </p>
              </div>

              <Button 
                onClick={handleGetLink}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-12 rounded-lg text-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                <ExternalLink className="mr-3 h-6 w-6" />
                Get Your Link
              </Button>
            </div>
          )}

          {/* Final Countdown after Get Link button clicked */}
          {finalCountdown > 0 && showGetLink && !showFinalLink && (
            <div className="mb-8">
              <div className="bg-orange-600 bg-opacity-30 border border-orange-500 rounded-lg p-6">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Clock className="h-6 w-6 text-orange-400" />
                  <span className="text-2xl font-bold text-white">{finalCountdown}</span>
                </div>
                <p className="text-gray-300">Preparing your link...</p>
              </div>
            </div>
          )}

          {/* Final Link Button */}
          {showFinalLink && (
            <div className="mb-8">
              <div className="bg-purple-600 bg-opacity-30 border border-purple-500 rounded-lg p-6 mb-6">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <ExternalLink className="h-8 w-8 text-purple-400" />
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <p className="text-purple-400 text-xl font-bold mb-2">
                  🎉 Ready to Access Your Link!
                </p>
                <p className="text-gray-300">
                  Click the button below to be redirected to your destination
                </p>
              </div>

              <Button 
                onClick={handleFinalRedirect}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-6 px-16 rounded-lg text-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 animate-pulse"
              >
                <ExternalLink className="mr-4 h-8 w-8" />
                Access Your Link Now
              </Button>
            </div>
          )}

          {/* Ad Space */}
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8 mt-8">
            <p className="text-gray-300">Advertisement Space</p>
          </div>

          {/* Security Info */}
          <div className="mt-8 text-sm text-gray-400">
            <p className="mb-2">🔒 This link has been verified by our security system</p>
            <p>⚡ Powered by GameHub Secure Gateway</p>
          </div>
        </div>
      </div>
    </div>
  );
}