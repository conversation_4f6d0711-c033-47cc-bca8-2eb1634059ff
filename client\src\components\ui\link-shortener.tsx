import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Co<PERSON>, Slice, BarChart3 } from "lucide-react";
import { <PERSON><PERSON> } from "./button";
import { Input } from "./input";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface ShortenedLink {
  id: number;
  originalUrl: string;
  shortUrl: string;
  shortCode: string;
  clicks: number;
  createdAt: string;
}

export default function LinkShortener() {
  const [urlInput, setUrlInput] = useState("");
  const [shortenedUrl, setShortenedUrl] = useState("");
  const [showResult, setShowResult] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: recentLinks, isLoading: linksLoading } = useQuery<ShortenedLink[]>({
    queryKey: ["/api/links/recent"],
  });

  const shortenMutation = useMutation({
    mutationFn: async (originalUrl: string) => {
      const response = await apiRequest("POST", "/api/links/shorten", { originalUrl });
      return response.json();
    },
    onSuccess: (data) => {
      setShortenedUrl(data.shortUrl);
      setShowResult(true);
      setUrlInput("");
      queryClient.invalidateQueries({ queryKey: ["/api/links/recent"] });
      toast({
        title: "Link shortened successfully!",
        description: "Your link has been shortened and is ready to share.",
      });
    },
    onError: (error: any) => {
      let errorMessage = "Failed to shorten the link. Please try again.";
      
      if (error.message) {
        // Extract more meaningful error message
        if (error.message.includes("400:")) {
          const match = error.message.match(/400:\s*(.+)/);
          if (match) {
            errorMessage = match[1];
          }
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!urlInput.trim()) {
      toast({
        title: "Error",
        description: "Please enter a URL to shorten.",
        variant: "destructive",
      });
      return;
    }

    try {
      new URL(urlInput);
      shortenMutation.mutate(urlInput);
    } catch (error) {
      toast({
        title: "Error",
        description: "Please enter a valid URL.",
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shortenedUrl);
      toast({
        title: "Copied!",
        description: "Link copied to clipboard.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy link to clipboard.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <section id="shortener" className="py-16 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">🔗 Link Shortener</h2>
          <p className="text-gray-300 text-lg">Shorten your gaming links and track their performance</p>
        </div>
        
        {/* URL Shortener Form */}
        <div className="glassmorphism rounded-xl p-8 mb-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative">
              <Input
                type="url"
                placeholder="Enter your long URL here..."
                value={urlInput}
                onChange={(e) => setUrlInput(e.target.value)}
                className="w-full bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg px-4 py-4 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-4">
                <Link className="h-5 w-5 text-gray-400" />
              </div>
            </div>
            
            <Button 
              type="submit"
              disabled={shortenMutation.isPending}
              className="w-full bg-pink-500 hover:bg-pink-600 text-white font-semibold py-4 rounded-lg transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              <Slice className="mr-2 h-5 w-5" />
              {shortenMutation.isPending ? "Shortening..." : "Shorten Link"}
            </Button>
            
            {/* Shortened URL Result */}
            {showResult && shortenedUrl && (
              <div className="bg-green-500 bg-opacity-20 border border-green-500 border-opacity-30 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm text-gray-300 mb-1">Shortened URL:</p>
                    <p className="text-white font-mono text-lg break-all">{shortenedUrl}</p>
                  </div>
                  <Button 
                    onClick={copyToClipboard}
                    className="ml-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </form>
        </div>
        
        {/* Recent Links Analytics */}
        <div className="glassmorphism rounded-xl p-8">
          <h3 className="text-xl font-semibold text-white mb-6">📊 Recent Links</h3>
          
          {linksLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse p-4 bg-white bg-opacity-5 rounded-lg">
                  <div className="flex justify-between">
                    <div className="flex-1 space-y-2">
                      <div className="w-3/4 h-4 bg-white bg-opacity-20 rounded"></div>
                      <div className="w-1/2 h-3 bg-white bg-opacity-20 rounded"></div>
                    </div>
                    <div className="flex space-x-6">
                      <div className="w-12 h-8 bg-white bg-opacity-20 rounded"></div>
                      <div className="w-16 h-4 bg-white bg-opacity-20 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : recentLinks && recentLinks.length > 0 ? (
            <div className="space-y-4">
              {recentLinks.map((link) => (
                <div key={link.id} className="flex items-center justify-between p-4 bg-white bg-opacity-5 rounded-lg hover:bg-opacity-10 transition-all duration-200">
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium truncate">{link.shortUrl}</p>
                    <p className="text-gray-400 text-sm truncate">{link.originalUrl}</p>
                  </div>
                  <div className="flex items-center space-x-6 ml-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-pink-400">{link.clicks.toLocaleString()}</p>
                      <p className="text-xs text-gray-400">clicks</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-300">{formatDate(link.createdAt)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400">No shortened links yet. Create your first one above!</p>
            </div>
          )}
          
          <div className="text-center mt-6">
            <button className="text-pink-400 hover:text-pink-300 font-medium transition-colors duration-200 flex items-center justify-center mx-auto">
              <BarChart3 className="mr-2 h-5 w-5" />
              View Detailed Analytics
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
