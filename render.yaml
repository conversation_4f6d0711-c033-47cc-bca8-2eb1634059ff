services:
  # Web Service (No database needed - using Telegram storage)
  - type: web
    name: gaming-deals-tracker
    env: node
    plan: free
    region: oregon
    buildCommand: npm ci && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: BASE_URL
        value: https://gaming-deals-tracker.onrender.com
      - key: TELEGRAM_BOT_TOKEN
        value: **********************************************
      - key: TELEGRAM_CHANNEL_ID
        value: -1002166499041
      - key: TEST_BOT_TOKEN
        value: 7817954175:AAGICw_FASiks6345fDRW-1d0oeGb0jbdfM
      - key: TEST_CHANNEL_ID
        value: -1002795432631
      - key: TELEGRAM_ADMIN_IDS
        value: 8100645535
      - key: JWT_SECRET
        value: your-production-jwt-secret-change-this