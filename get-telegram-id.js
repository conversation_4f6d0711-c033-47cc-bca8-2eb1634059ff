// Simple script to help you get your Telegram user ID
console.log('🔍 How to Get Your Telegram Admin ID:\n');

console.log('📱 METHOD 1: Using @userinfobot');
console.log('1. Open Telegram and search for @userinfobot');
console.log('2. Start a chat with the bot');
console.log('3. Send any message to the bot');
console.log('4. The bot will reply with your user ID');
console.log('5. Copy the ID number (e.g., 123456789)');

console.log('\n📱 METHOD 2: Using @getidsbot');
console.log('1. Open Telegram and search for @getidsbot');
console.log('2. Start a chat with the bot');
console.log('3. Send /start to the bot');
console.log('4. The bot will show your user ID');

console.log('\n📱 METHOD 3: Using our test bot');
console.log('1. Send a message to your Gaming Deals Tracker bot');
console.log('2. Check the server logs for your Telegram ID');
console.log('3. Look for messages like "Message from user ID: 123456789"');

console.log('\n🔧 CONFIGURATION STEPS:');
console.log('1. Get your Telegram user ID using one of the methods above');
console.log('2. Open the .env file in your project');
console.log('3. Update TELEGRAM_ADMIN_IDS with your real ID:');
console.log('   TELEGRAM_ADMIN_IDS=YOUR_REAL_ID_HERE');
console.log('4. For multiple admins, separate with commas:');
console.log('   TELEGRAM_ADMIN_IDS=123456789,987654321,555666777');
console.log('5. Restart your server');

console.log('\n✅ EXAMPLE .env CONFIGURATION:');
console.log('TELEGRAM_BOT_TOKEN=**********************************************');
console.log('TELEGRAM_CHANNEL_ID=-1002795432631');
console.log('TELEGRAM_ADMIN_IDS=123456789  # Replace with your real ID');

console.log('\n🎯 TESTING:');
console.log('1. Update .env with your real Telegram ID');
console.log('2. Restart the server: npm run dev');
console.log('3. Make a withdrawal request on the website');
console.log('4. Check your Telegram - you should receive a private message');
console.log('5. The message will have buttons: "Payment Done" and "Reject Payment"');

console.log('\n📋 CURRENT CONFIGURATION:');
console.log('Admin IDs in .env:', process.env.TELEGRAM_ADMIN_IDS || 'Not set');

console.log('\n🚀 Once configured correctly, admins will receive:');
console.log('• Private messages with withdrawal details');
console.log('• Interactive buttons for approval/rejection');
console.log('• Complete user information and payment details');
console.log('• Instant action capabilities');

console.log('\n💡 TIP: Test with a small withdrawal first to make sure everything works!');
