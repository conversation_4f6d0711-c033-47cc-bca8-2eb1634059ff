import { useQuery } from "@tanstack/react-query";
import { Clock, ShoppingCart, Flame } from "lucide-react";
import type { Deal } from "@shared/schema";

export default function DealsSection() {
  const { data: deals, isLoading, error } = useQuery<Deal[]>({
    queryKey: ["/api/deals"],
  });

  if (error) {
    return (
      <section id="deals" className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-red-400">Failed to load deals. Please try again later.</p>
        </div>
      </section>
    );
  }

  return (
    <section id="deals" className="py-16 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-2">
              🔥 Hot Gaming Deals
            </h2>
            <p className="text-gray-300">Discover incredible savings on your favorite games and gear</p>
          </div>
          <div className="hidden md:block text-sm text-gray-300">
            <Clock className="inline mr-2 h-4 w-4" />
            Limited Time Offers
          </div>
        </div>
        
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="glassmorphism rounded-xl p-6 animate-pulse">
                <div className="w-full h-40 bg-white bg-opacity-20 rounded-lg mb-4"></div>
                <div className="space-y-3">
                  <div className="w-full h-6 bg-white bg-opacity-20 rounded"></div>
                  <div className="flex space-x-2">
                    <div className="w-20 h-8 bg-white bg-opacity-20 rounded"></div>
                    <div className="w-16 h-6 bg-white bg-opacity-20 rounded"></div>
                  </div>
                  <div className="flex justify-between">
                    <div className="w-16 h-6 bg-white bg-opacity-20 rounded-full"></div>
                    <div className="w-16 h-4 bg-white bg-opacity-20 rounded"></div>
                  </div>
                  <div className="w-full h-8 bg-white bg-opacity-20 rounded-lg"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {deals?.map((deal) => (
              <div key={deal.id} className="glassmorphism rounded-xl p-6 hover-lift group">
                <img 
                  src={deal.imageUrl} 
                  alt={deal.title} 
                  className="w-full h-40 object-cover rounded-lg mb-4 group-hover:scale-105 transition-transform duration-200" 
                />
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-white">{deal.title}</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-green-400">{deal.currentPrice}</span>
                    <span className="text-sm text-gray-400 line-through">{deal.originalPrice}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                      {deal.discount}
                    </span>
                    <span className="text-xs text-gray-400">{deal.timeLeft}</span>
                  </div>
                  <button className="w-full bg-pink-500 hover:bg-pink-600 text-white font-semibold py-2 rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Get Deal
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
        
        <div className="text-center mt-10">
          <button className="glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center mx-auto">
            <Flame className="mr-2 h-5 w-5" />
            View All Deals
          </button>
        </div>
      </div>
    </section>
  );
}
