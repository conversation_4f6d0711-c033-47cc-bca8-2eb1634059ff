# GameHub Application

## Overview

GameHub is a full-stack web application that serves as a comprehensive gaming hub featuring news, deals, and link shortening services. The application combines a modern React frontend with a Node.js/Express backend, utilizing PostgreSQL for data persistence and providing a sleek gaming-themed user interface.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized production builds
- **UI Framework**: Tailwind CSS with shadcn/ui component library
- **State Management**: TanStack Query (React Query) for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Styling**: Tailwind CSS with custom gaming theme colors and glassmorphism effects

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **API Design**: RESTful API endpoints with JSON responses
- **Error Handling**: Centralized error middleware with proper HTTP status codes
- **Logging**: Custom request logging middleware for API endpoints

### Database Layer
- **Database**: PostgreSQL (configured for Render managed PostgreSQL) - ACTIVE
- **ORM**: Drizzle ORM with type-safe schema definitions  
- **Migration Strategy**: Drizzle Kit for schema migrations
- **Connection**: Standard PostgreSQL connection pooling via 'pg' driver
- **Storage**: DatabaseStorage class with full persistence support
- **Production URL**: ***********************************************************************************************

## Key Components

### Core Features
1. **Gaming News Section**: Displays latest gaming news with category filtering and responsive cards
2. **Gaming Deals Section**: Shows hot gaming deals with pricing, discounts, and time-limited offers
3. **Link Shortener**: URL shortening service with click tracking and recent links display
4. **Multi-Step Redirect System**: Complex redirect flow with gaming content and ad integration
   - Step 1: Gaming news blog with 20s + 10s countdowns and scroll requirements
   - Step 2: Gaming career blog with 10s + 20s countdowns and scroll requirements
   - Final: Secure gateway with 20s + 5s countdowns before final link access
5. **Ad Integration**: Comprehensive ad system with both Monetag MultiTag and native banner (interstitial) ads integrated across all redirect pages
6. **Responsive Navigation**: Smooth scrolling navigation with glassmorphism design

### Data Models
- **Users**: Authentication ready schema with username/password
- **Shortened Links**: URL shortening with click analytics and timestamps
- **Content Types**: Environment-based news articles and deals configuration

### UI Components
- **Design System**: Comprehensive shadcn/ui component library
- **Theme**: Gaming-focused color palette with purple, blue, and pink accents
- **Responsiveness**: Mobile-first design with breakpoint-aware components
- **Animations**: Custom CSS animations and transitions for enhanced UX

## Data Flow

### Frontend to Backend
1. React components use TanStack Query for data fetching
2. API requests handled through centralized query client with error handling
3. Form submissions use mutations with optimistic updates
4. Real-time UI updates through query invalidation

### Backend Processing
1. Express routes handle API endpoints with validation
2. Storage layer abstracts data access (currently in-memory with database ready)
3. Middleware chain handles logging, parsing, and error responses
4. Environment-based content loading for news and deals

### Database Operations
1. Drizzle ORM provides type-safe database operations
2. Schema definitions shared between client and server via `shared/` directory
3. Zod validation schemas for runtime type checking
4. Migration-based schema evolution

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL connection for serverless environments
- **drizzle-orm & drizzle-kit**: Type-safe ORM and migration tools
- **@tanstack/react-query**: Server state management and caching
- **@radix-ui/***: Accessible UI primitives for component library
- **wouter**: Lightweight React router
- **nanoid**: Secure URL-safe ID generation

### Development Tools
- **Vite**: Build tool with HMR and optimized bundling
- **TypeScript**: Type safety across the entire stack
- **Tailwind CSS**: Utility-first CSS framework
- **PostCSS**: CSS processing and optimization

### Replit Integration
- **@replit/vite-plugin-cartographer**: Development environment integration
- **@replit/vite-plugin-runtime-error-modal**: Enhanced error reporting

## Deployment Strategy

### Development Environment
- **Hot Module Replacement**: Vite dev server with React Fast Refresh
- **Type Checking**: Real-time TypeScript compilation and error reporting
- **Database**: Automatic schema synchronization with `db:push` command
- **Environment Variables**: DATABASE_URL and content configuration via env vars

### Production Deployment (Render)
1. **Frontend**: Vite builds optimized static assets to `dist/public`
2. **Backend**: esbuild bundles server code to `dist/index.js`
3. **Database**: PostgreSQL database with automatic migrations
4. **Static Assets**: Served through Express in production mode
5. **Environment Variables**: Content loaded from JSON-formatted environment variables

### Configuration Management
- **Environment-based**: Production-ready configuration for Render deployment
- **Content Management**: News and deals loaded from NEWS_X and DEAL_X environment variables
- **Database**: PostgreSQL connection via DATABASE_URL environment variable
- **Ad Integration**: Monetag MultiTag and native banner ads configured for production
- **Build Process**: Automated build pipeline for Render deployment

### Content Configuration
- **News Articles**: Configure via NEWS_1, NEWS_2, etc. as JSON objects
- **Gaming Deals**: Configure via DEAL_1, DEAL_2, etc. as JSON objects
- **No Fallback Content**: Production version requires environment variable configuration
- **Image URLs**: Support for external image hosting services

The application is production-ready for Render deployment with comprehensive documentation, environment variable configuration, and ad monetization systems integrated.