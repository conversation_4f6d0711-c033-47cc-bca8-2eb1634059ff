import { <PERSON><PERSON>, User, DollarSign } from "lucide-react";
import { Link } from "wouter";

export default function Navbar() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <nav className="glassmorphism relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Brand */}
          <div className="flex items-center space-x-2">
            <Gamepad className="text-white text-2xl" />
            <span className="text-white text-xl font-bold">GameHub</span>
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <button 
                onClick={() => scrollToSection('news')}
                className="text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                News
              </button>
              <button 
                onClick={() => scrollToSection('deals')}
                className="text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                Deals
              </button>
              <button
                onClick={() => scrollToSection('shortener')}
                className="text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                Link Shortener
              </button>
              <button
                onClick={() => scrollToSection('shortener')}
                className="flex items-center text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                <DollarSign className="w-4 h-4 mr-1" />
                Start Earning
              </button>
              <Link href="/dashboard">
                <a className="flex items-center bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 text-sm font-medium">
                  <User className="w-4 h-4 mr-1" />
                  Dashboard
                </a>
              </Link>
            </div>
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button type="button" className="text-white hover:text-pink-400">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
