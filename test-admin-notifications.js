// Test the admin notification system
async function testAdminNotifications() {
  try {
    console.log('🧪 Testing Admin Notification System...\n');
    
    // Test 1: Create user and make withdrawal request
    console.log('1. Creating user and testing withdrawal notification...');
    const username = 'admin_test_' + Date.now();
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password: 'test123' })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ User created:', username);
    const token = registerData.token;
    
    // Add payment details
    await fetch('http://localhost:3000/api/user/payment-details', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({
        fullName: 'Admin Test User',
        address: '123 Admin Test Street, Test City, TC 12345, USA',
        phoneNumber: '+*********0',
        telegramUsername: '@admintest',
        trafficSource: 'Testing admin notification system',
        trafficSourceLinks: '• YouTube: https://youtube.com/@admintest\n• Website: https://admintest.com',
        paymentMethod: 'upi',
        paymentDetails: 'admintest@paytm'
      })
    });
    console.log('✅ Payment details added');
    
    // Set balance above minimum
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({ balance: 12.50 })
    });
    console.log('✅ Balance set to $12.50');
    
    // Make withdrawal request
    const withdrawResponse = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({ amount: 8.00 })
    });
    const withdrawData = await withdrawResponse.json();
    console.log('✅ Withdrawal request:', withdrawData.message);
    
    // Test 2: Create user with low balance
    console.log('\n2. Testing low balance withdrawal notification...');
    const username2 = 'low_admin_test_' + Date.now();
    const registerResponse2 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: username2, password: 'test123' })
    });
    
    const registerData2 = await registerResponse2.json();
    console.log('✅ User created:', username2);
    const token2 = registerData2.token;
    
    // Set low balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token2}` },
      body: JSON.stringify({ balance: 1.75 })
    });
    console.log('✅ Balance set to $1.75');
    
    // Make withdrawal request
    const withdrawResponse2 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token2}` },
      body: JSON.stringify({ amount: 1.50 })
    });
    const withdrawData2 = await withdrawResponse2.json();
    console.log('✅ Low balance withdrawal:', withdrawData2.message);
    
    console.log('\n🎉 ADMIN NOTIFICATION TESTS COMPLETED!');
    console.log('\n📊 ADMIN NOTIFICATION SYSTEM:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('✅ PRIVATE MESSAGES: Admins get withdrawal requests privately');
    console.log('✅ INTERACTIVE BUTTONS: Payment Done/Reject buttons included');
    console.log('✅ CHANNEL NOTIFICATION: Simple notification posted to channel');
    console.log('✅ ADMIN CONFIGURATION: Admin IDs configurable via environment');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    console.log('\n📱 ADMIN EXPERIENCE:');
    console.log('• ✅ Receive detailed withdrawal requests privately');
    console.log('• ✅ Click "Payment Done" to approve withdrawal');
    console.log('• ✅ Click "Reject Payment" to reject and restore balance');
    console.log('• ✅ Click "Contact User" to get contact information');
    console.log('• ✅ Click "View User Details" to see complete profile');
    
    console.log('\n🔧 CONFIGURATION:');
    console.log('• Admin IDs: Set TELEGRAM_ADMIN_IDS in .env file');
    console.log('• Format: TELEGRAM_ADMIN_IDS=*********,*********');
    console.log('• Multiple admins supported (comma-separated)');
    
    console.log('\n📋 WHAT ADMINS RECEIVE:');
    console.log('• Complete user information and withdrawal details');
    console.log('• Payment method and contact information');
    console.log('• Account statistics and balance verification');
    console.log('• Interactive buttons for immediate action');
    console.log('• Clear status indicators for processing decisions');
    
    console.log('\n🚀 SYSTEM STATUS: Admin notifications configured!');
    console.log('📝 NOTE: Update TELEGRAM_ADMIN_IDS in .env with real admin Telegram IDs');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAdminNotifications();
