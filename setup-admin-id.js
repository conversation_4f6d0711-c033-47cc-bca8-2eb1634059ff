// Help script to set up admin ID
console.log('🔧 SETUP ADMIN ID FOR WITHDRAWAL NOTIFICATIONS\n');

console.log('📱 STEP 1: Get Your Telegram ID');
console.log('═══════════════════════════════════════════════');
console.log('Option A: Use your Gaming Deals Tracker bot');
console.log('1. Open Telegram and find your bot: @DileVdbot');
console.log('2. Send the command: /id');
console.log('3. The bot will reply with your Telegram ID');
console.log('4. Copy the User ID number');
console.log('');
console.log('Option B: Use @userinfobot');
console.log('1. Search for @userinfobot in Telegram');
console.log('2. Start a chat and send any message');
console.log('3. Copy the ID number from the response');

console.log('\n🔧 STEP 2: Update Configuration');
console.log('═══════════════════════════════════════════════');
console.log('1. Open the .env file in your project folder');
console.log('2. Find the line: TELEGRAM_ADMIN_IDS=*********,*********');
console.log('3. Replace with your real ID: TELEGRAM_ADMIN_IDS=YOUR_REAL_ID');
console.log('4. Save the file');

console.log('\n🔄 STEP 3: Restart Server');
console.log('═══════════════════════════════════════════════');
console.log('1. Stop the current server (Ctrl+C)');
console.log('2. Start it again: npm run dev');
console.log('3. Wait for "Server is running" message');

console.log('\n🧪 STEP 4: Test the System');
console.log('═══════════════════════════════════════════════');
console.log('1. Go to http://localhost:3000');
console.log('2. Register/login to your account');
console.log('3. Make a withdrawal request (any amount)');
console.log('4. Check your Telegram - you should receive a private message');
console.log('5. The message will have buttons: "Payment Done" and "Reject Payment"');

console.log('\n📋 CURRENT STATUS:');
console.log('═══════════════════════════════════════════════');
const currentAdminIds = process.env.TELEGRAM_ADMIN_IDS || 'Not configured';
console.log('Current admin IDs:', currentAdminIds);

if (currentAdminIds === '*********,*********' || currentAdminIds === 'Not configured') {
  console.log('❌ ISSUE: Using default/fake admin IDs');
  console.log('✅ SOLUTION: Follow steps above to set your real Telegram ID');
} else {
  console.log('✅ Admin IDs configured! Test with a withdrawal request.');
}

console.log('\n💡 EXAMPLE CONFIGURATION:');
console.log('═══════════════════════════════════════════════');
console.log('If your Telegram ID is 555666777, your .env should have:');
console.log('TELEGRAM_ADMIN_IDS=555666777');
console.log('');
console.log('For multiple admins:');
console.log('TELEGRAM_ADMIN_IDS=555666777,888999000,111222333');

console.log('\n🎯 WHAT YOU\'LL RECEIVE:');
console.log('═══════════════════════════════════════════════');
console.log('• Private Telegram message with withdrawal details');
console.log('• User information (name, payment method, balance)');
console.log('• Interactive buttons for instant approval/rejection');
console.log('• Contact information and user statistics');

console.log('\n🚨 TROUBLESHOOTING:');
console.log('═══════════════════════════════════════════════');
console.log('If you don\'t receive messages:');
console.log('1. Make sure your Telegram ID is correct');
console.log('2. Check that you\'ve restarted the server');
console.log('3. Verify the bot token is working');
console.log('4. Try sending /id to your bot first');

console.log('\n🚀 Ready to test? Follow the steps above!');
