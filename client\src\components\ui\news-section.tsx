import { useQuery } from "@tanstack/react-query";
import { RotateCcw } from "lucide-react";
import type { NewsArticle } from "@shared/schema";

export default function NewsSection() {
  const { data: news, isLoading, error } = useQuery<NewsArticle[]>({
    queryKey: ["/api/news"],
  });

  if (error) {
    return (
      <section id="news" className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-red-400">Failed to load news. Please try again later.</p>
        </div>
      </section>
    );
  }

  return (
    <section id="news" className="py-16 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-2">Latest Gaming News</h2>
            <p className="text-gray-300">Stay informed with the latest happenings in gaming</p>
          </div>
          <div className="hidden md:block text-sm text-gray-300">
            <RotateCcw className="inline mr-2 h-4 w-4" />
            Updates Automatically
          </div>
        </div>
        
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="glassmorphism rounded-xl p-6 animate-pulse">
                <div className="w-full h-48 bg-white bg-opacity-20 rounded-lg mb-4"></div>
                <div className="space-y-3">
                  <div className="w-20 h-6 bg-white bg-opacity-20 rounded-full"></div>
                  <div className="w-full h-6 bg-white bg-opacity-20 rounded"></div>
                  <div className="w-full h-12 bg-white bg-opacity-20 rounded"></div>
                  <div className="flex justify-between">
                    <div className="w-16 h-4 bg-white bg-opacity-20 rounded"></div>
                    <div className="w-16 h-4 bg-white bg-opacity-20 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {news?.map((article) => (
              <article key={article.id} className="glassmorphism rounded-xl p-6 hover-lift cursor-pointer">
                <img 
                  src={article.imageUrl} 
                  alt={article.title} 
                  className="w-full h-48 object-cover rounded-lg mb-4" 
                />
                <div className="space-y-3">
                  <span className="inline-block bg-purple-600 text-white text-xs px-3 py-1 rounded-full font-medium">
                    {article.category}
                  </span>
                  <h3 className="text-xl font-semibold text-white leading-tight">
                    {article.title}
                  </h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span>{article.date}</span>
                    <span>{article.readTime}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}
        
        <div className="text-center mt-10">
          <button className="glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200">
            <span className="mr-2">+</span>
            Load More News
          </button>
        </div>
      </div>
    </section>
  );
}
