// Simple API test script
// Using built-in fetch (Node.js 18+)

async function testAPI() {
  try {
    console.log('🧪 Testing Gaming Deals Tracker API...\n');
    
    // Test 1: Register a new user
    console.log('1. Testing user registration...');
    const registerResponse = await fetch('http://localhost:5000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'testuser10',
        password: 'testpass123'
      })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ Registration:', registerData.message);
    console.log('🔑 Token received:', registerData.token ? 'Yes' : 'No');
    
    if (!registerData.token) {
      console.log('❌ No token received, stopping tests');
      return;
    }
    
    const token = registerData.token;
    
    // Test 2: Test payment details with validation
    console.log('\n2. Testing payment details validation...');
    
    // Test with invalid data first
    console.log('   Testing with invalid data...');
    const invalidPaymentResponse = await fetch('http://localhost:5000/api/user/payment-details', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        fullName: 'A', // Too short
        address: '123', // Too short
        phoneNumber: '123', // Too short
        telegramUsername: '', // Empty
        trafficSource: '123', // Too short
        trafficSourceLinks: '123', // Too short
        paymentMethod: 'upi',
        paymentDetails: '123' // Too short
      })
    });
    
    const invalidPaymentData = await invalidPaymentResponse.json();
    console.log('❌ Invalid data result:', invalidPaymentData.message);
    console.log('📝 Validation errors:', invalidPaymentData.errors?.length || 0, 'errors found');
    
    // Test with valid data
    console.log('   Testing with valid data...');
    const validPaymentResponse = await fetch('http://localhost:5000/api/user/payment-details', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        fullName: 'Test User Ten',
        address: '123 Complete Address Street, Test City, TC 12345, USA',
        phoneNumber: '+1234567890',
        telegramUsername: '@testuser10',
        trafficSource: 'Complete traffic source description for testing',
        trafficSourceLinks: '• YouTube: https://youtube.com/@testuser10\n• Instagram: https://instagram.com/testuser10\n• Website: https://testuser10.com',
        paymentMethod: 'upi',
        paymentDetails: 'testuser10@paytm'
      })
    });
    
    const validPaymentData = await validPaymentResponse.json();
    console.log('✅ Valid data result:', validPaymentData.message);
    
    // Test 3: Create a link
    console.log('\n3. Testing link creation...');
    const linkResponse = await fetch('http://localhost:5000/api/links/shorten', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        originalUrl: 'https://example.com'
      })
    });
    
    const linkData = await linkResponse.json();
    console.log('✅ Link created:', linkData.shortCode);
    console.log('🔗 Short URL:', linkData.shortUrl);
    
    // Test 4: Get user profile
    console.log('\n4. Testing user profile...');
    const profileResponse = await fetch('http://localhost:5000/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const profileData = await profileResponse.json();
    console.log('✅ Profile loaded:', profileData.user.username);
    console.log('💰 Balance:', `$${profileData.user.availableBalance.toFixed(3)}`);
    console.log('🔗 Total Links:', profileData.user.totalLinks);
    console.log('💳 Payment Method:', profileData.user.paymentMethod || 'Not set');
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
