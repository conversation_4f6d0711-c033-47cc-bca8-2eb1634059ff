// Test withdrawal validation fix
async function testWithdrawalValidation() {
  try {
    console.log('🔍 Testing Withdrawal Validation Fix\n');
    
    // Create test user
    console.log('1. Creating test user...');
    const username = 'validation_test_' + Date.now();
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password: 'test123' })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ User created:', username);
    const token = registerData.token;
    
    // Set small balance like your case
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({ balance: 0.008 })
    });
    console.log('✅ Balance set to $0.008 (same as your case)');
    
    // Test different withdrawal amounts
    const testAmounts = [0.008, 0.001, 0.0005, 0.01, 0.1];
    
    for (const amount of testAmounts) {
      console.log(`\n2. Testing withdrawal of $${amount}...`);
      
      const withdrawResponse = await fetch('http://localhost:3000/api/user/withdraw', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${token}` 
        },
        body: JSON.stringify({ amount })
      });
      
      const withdrawData = await withdrawResponse.json();
      
      if (withdrawResponse.ok) {
        console.log(`✅ SUCCESS: $${amount} - ${withdrawData.message}`);
      } else {
        console.log(`❌ FAILED: $${amount} - ${withdrawData.message}`);
        if (withdrawData.errors) {
          console.log('   Validation errors:', withdrawData.errors);
        }
      }
      
      // Reset balance for next test
      if (withdrawResponse.ok) {
        await fetch('http://localhost:3000/api/test/set-balance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
          body: JSON.stringify({ balance: 0.008 })
        });
      }
    }
    
    console.log('\n🎉 WITHDRAWAL VALIDATION TEST COMPLETED!');
    console.log('\n📊 VALIDATION RULES:');
    console.log('═══════════════════════════════════════════════');
    console.log('✅ **Updated minimum:** $0.001 (was $0.01)');
    console.log('✅ **Your amount:** $0.008 should now work');
    console.log('✅ **Enhanced logging:** Better error reporting');
    console.log('✅ **Always deduct:** Money deducted regardless of amount');
    
    console.log('\n🔧 WHAT WAS FIXED:');
    console.log('═══════════════════════════════════════════════');
    console.log('• **Validation minimum:** Reduced from $0.01 to $0.001');
    console.log('• **Error logging:** Added detailed validation error logging');
    console.log('• **Amount handling:** Better precision for small amounts');
    console.log('• **Always deduct:** Money deducted immediately on request');
    
    console.log('\n💡 YOUR SPECIFIC CASE:');
    console.log('═══════════════════════════════════════════════');
    console.log('• **Your balance:** $0.008');
    console.log('• **Old validation:** Failed (< $0.01)');
    console.log('• **New validation:** Should pass (> $0.001)');
    console.log('• **Result:** Money should be deducted immediately');
    
    console.log('\n🎯 TRY AGAIN:');
    console.log('═══════════════════════════════════════════════');
    console.log('1. Go to your dashboard');
    console.log('2. Try withdrawing $0.008 again');
    console.log('3. It should work now!');
    console.log('4. Money will be deducted immediately');
    console.log('5. Admin will receive notification with buttons');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testWithdrawalValidation();
