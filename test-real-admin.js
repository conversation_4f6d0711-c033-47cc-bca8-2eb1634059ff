// Test with real admin ID
async function testRealAdmin() {
  try {
    console.log('🧪 Testing with Real Admin ID: 8100645535\n');
    
    // Create a test user and withdrawal
    console.log('1. Creating test user...');
    const username = 'real_admin_test_' + Date.now();
    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password: 'test123' })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ User created:', username);
    const token = registerData.token;
    
    // Add payment details
    await fetch('http://localhost:3000/api/user/payment-details', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({
        fullName: 'Real Admin Test User',
        address: '123 Real Admin Street, Test City, TC 12345, India',
        phoneNumber: '+91-9876543210',
        telegramUsername: '@realadmintest',
        trafficSource: 'Testing real admin notification system',
        trafficSourceLinks: '• YouTube: https://youtube.com/@realadmintest\n• Instagram: https://instagram.com/realadmintest',
        paymentMethod: 'upi',
        paymentDetails: 'realadmintest@paytm'
      })
    });
    console.log('✅ Payment details added');
    
    // Set balance above minimum
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({ balance: 10.00 })
    });
    console.log('✅ Balance set to $10.00');
    
    // Make withdrawal request
    console.log('\n2. Making withdrawal request...');
    const withdrawResponse = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
      body: JSON.stringify({ amount: 6.50 })
    });
    const withdrawData = await withdrawResponse.json();
    console.log('✅ Withdrawal request:', withdrawData.message);
    
    console.log('\n🎉 TEST COMPLETED!');
    console.log('\n📱 CHECK YOUR TELEGRAM NOW!');
    console.log('═══════════════════════════════════════════════');
    console.log('You should receive a private message with:');
    console.log('• Complete withdrawal details');
    console.log('• User information and payment method');
    console.log('• Interactive buttons:');
    console.log('  - ✅ Payment Done');
    console.log('  - ❌ Reject Payment');
    console.log('  - 📞 Contact User');
    console.log('  - 🔍 View User Details');
    
    console.log('\n🔧 ADMIN ACTIONS:');
    console.log('═══════════════════════════════════════════════');
    console.log('• Click "Payment Done" to approve the withdrawal');
    console.log('• Click "Reject Payment" to reject and restore balance');
    console.log('• Click "Contact User" to get contact information');
    console.log('• Click "View User Details" to see complete profile');
    
    console.log('\n📊 WITHDRAWAL DETAILS:');
    console.log('═══════════════════════════════════════════════');
    console.log(`• User: ${username}`);
    console.log('• Amount: $6.50');
    console.log('• Payment Method: UPI (🇮🇳)');
    console.log('• Balance After: $3.50');
    console.log('• Status: Ready for Payment (meets all requirements)');
    
    console.log('\n💡 WHAT TO EXPECT:');
    console.log('═══════════════════════════════════════════════');
    console.log('1. Private Telegram message to ID: 8100645535');
    console.log('2. Formatted message with all user details');
    console.log('3. Four interactive buttons for admin actions');
    console.log('4. Simple notification in the channel');
    
    console.log('\n🚨 IF YOU DON\'T RECEIVE THE MESSAGE:');
    console.log('═══════════════════════════════════════════════');
    console.log('1. Make sure you\'ve started a chat with the bot first');
    console.log('2. Send /start to the bot to initiate conversation');
    console.log('3. Check server logs for any error messages');
    console.log('4. Verify your Telegram ID is correct: 8100645535');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testRealAdmin();
