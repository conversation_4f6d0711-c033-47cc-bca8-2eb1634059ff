// Test the always-enabled withdraw functionality
async function testAlwaysEnabledWithdraw() {
  try {
    console.log('🧪 Testing Always-Enabled Withdraw Functionality...\n');
    
    // Test 1: User with very low balance (below $4)
    console.log('1. Testing user with LOW balance (below $4)...');
    const user1 = 'low_balance_' + Date.now();
    const register1 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: user1, password: 'test123' })
    });
    const data1 = await register1.json();
    console.log('✅ User created:', user1);
    
    // Set very low balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data1.token}` },
      body: JSON.stringify({ balance: 1.50 })
    });
    console.log('✅ Balance set to $1.50 (below minimum)');
    
    // Try to withdraw
    const withdraw1 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data1.token}` },
      body: JSON.stringify({ amount: 1.00 })
    });
    const withdrawData1 = await withdraw1.json();
    console.log('✅ Withdrawal with low balance:', withdrawData1.message);
    
    // Test 2: User with medium balance (between $1-$4)
    console.log('\n2. Testing user with MEDIUM balance ($2-$4)...');
    const user2 = 'medium_balance_' + Date.now();
    const register2 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: user2, password: 'test123' })
    });
    const data2 = await register2.json();
    console.log('✅ User created:', user2);
    
    // Set medium balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data2.token}` },
      body: JSON.stringify({ balance: 3.25 })
    });
    console.log('✅ Balance set to $3.25 (below minimum)');
    
    // Try to withdraw
    const withdraw2 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data2.token}` },
      body: JSON.stringify({ amount: 2.50 })
    });
    const withdrawData2 = await withdraw2.json();
    console.log('✅ Withdrawal with medium balance:', withdrawData2.message);
    
    // Test 3: User with high balance (above $4)
    console.log('\n3. Testing user with HIGH balance (above $4)...');
    const user3 = 'high_balance_' + Date.now();
    const register3 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: user3, password: 'test123' })
    });
    const data3 = await register3.json();
    console.log('✅ User created:', user3);
    
    // Set high balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data3.token}` },
      body: JSON.stringify({ balance: 8.75 })
    });
    console.log('✅ Balance set to $8.75 (above minimum)');
    
    // Try to withdraw
    const withdraw3 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data3.token}` },
      body: JSON.stringify({ amount: 5.00 })
    });
    const withdrawData3 = await withdraw3.json();
    console.log('✅ Withdrawal with high balance:', withdrawData3.message);
    
    // Test 4: User with exactly $4 balance
    console.log('\n4. Testing user with EXACT minimum balance ($4.00)...');
    const user4 = 'exact_balance_' + Date.now();
    const register4 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: user4, password: 'test123' })
    });
    const data4 = await register4.json();
    console.log('✅ User created:', user4);
    
    // Set exact minimum balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data4.token}` },
      body: JSON.stringify({ balance: 4.00 })
    });
    console.log('✅ Balance set to $4.00 (exact minimum)');
    
    // Try to withdraw
    const withdraw4 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${data4.token}` },
      body: JSON.stringify({ amount: 4.00 })
    });
    const withdrawData4 = await withdraw4.json();
    console.log('✅ Withdrawal with exact minimum:', withdrawData4.message);
    
    console.log('\n🎉 ALL TESTS COMPLETED!');
    console.log('\n📊 ALWAYS-ENABLED WITHDRAW SUMMARY:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('✅ WITHDRAW BUTTON: Always enabled for all users');
    console.log('✅ LOW BALANCE: Users can request withdrawals below $4');
    console.log('✅ WARNINGS: Clear messages about minimum payout');
    console.log('✅ ADMIN CONTROL: Balance deduction only when requirements met');
    console.log('✅ TELEGRAM POSTS: Enhanced with balance warnings and status');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    console.log('\n💡 KEY FEATURES:');
    console.log('• Users can make withdrawal requests with ANY balance');
    console.log('• Clear warnings shown for balances below $4.00');
    console.log('• Admin gets detailed status in Telegram posts');
    console.log('• Balance only deducted when payment will be processed');
    console.log('• Minimum payout requirement clearly communicated');
    
    console.log('\n📱 Telegram Post Status Examples:');
    console.log('• Below $4: "⏳ PENDING BALANCE - User below minimum"');
    console.log('• Above $4: "✅ READY FOR PAYMENT - Process immediately"');
    console.log('• Missing details: "⚠️ INCOMPLETE DETAILS - Contact user"');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAlwaysEnabledWithdraw();
