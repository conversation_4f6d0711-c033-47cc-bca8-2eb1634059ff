import TelegramBot from 'node-telegram-bot-api';
import { storage } from './storage';

// Test bot configuration from environment
const TEST_BOT_TOKEN = process.env.TEST_BOT_TOKEN || '7817954175:AAGICw_FASiks6345fDRW-1d0oeGb0jbdfM';
const TEST_CHANNEL_ID = process.env.TEST_CHANNEL_ID || '-1002795432631';

console.log('🔧 Bot configuration:');
console.log('📱 TEST_BOT_TOKEN:', TEST_BOT_TOKEN ? 'Set' : 'Missing');
console.log('📺 TEST_CHANNEL_ID:', TEST_CHANNEL_ID);

export class UserRegistrationBot {
  private bot: TelegramBot;
  private userSessions: Map<string, any> = new Map();
  private completedRegistrations: Set<string> = new Set(); // Track completed registrations

  constructor() {
    try {
      this.bot = new TelegramBot(TEST_BOT_TOKEN, {
        polling: {
          interval: 1000,
          autoStart: true,
          params: {
            timeout: 10
          }
        }
      });

      this.setupHandlers();
      this.setupErrorHandlers();

      console.log('🤖 User Registration Bot started with polling');
      console.log('📱 Bot username should be: @DileVdbot');
      console.log('🔗 Users should message: https://t.me/DileVdbot');

      // Test bot connection
      this.testBotConnection();

      // Load completed registrations
      this.loadCompletedRegistrations();
    } catch (error) {
      console.error('❌ Failed to initialize registration bot:', error);
    }
  }

  private async loadCompletedRegistrations() {
    try {
      console.log('📂 Loading completed registrations from Telegram...');
      // This is a simplified approach - in a real implementation,
      // you'd fetch messages from the channel to rebuild the state
      console.log('💡 Note: Registration state will be rebuilt as users interact with the bot');
    } catch (error) {
      console.error('Error loading completed registrations:', error);
    }
  }

  private async testBotConnection() {
    try {
      const me = await this.bot.getMe();
      console.log(`✅ Bot connected successfully: @${me.username} (${me.first_name})`);
    } catch (error) {
      console.error('❌ Bot connection test failed:', error);
    }
  }

  private setupErrorHandlers() {
    this.bot.on('polling_error', (error) => {
      console.error('🔴 Telegram polling error:', error.message);
    });

    this.bot.on('error', (error) => {
      console.error('🔴 Telegram bot error:', error.message);
    });
  }

  private setupHandlers() {
    console.log('🔧 Setting up bot message handlers...');

    // Start command
    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`📱 /start command received from @${username} (ID: ${telegramId})`);

      if (!telegramId) {
        console.log('❌ No telegram ID found in message');
        return;
      }

      // Check if user already completed registration
      if (this.completedRegistrations.has(telegramId)) {
        console.log(`✅ User ${username} already registered, showing main menu`);
        this.showMainMenu(chatId, username);
        return;
      }

      console.log(`💬 Sending welcome message to chat ${chatId}`);

      this.bot.sendMessage(chatId,
        `🎉 Welcome to Gaming Deals Tracker Earnings Program!\n\n` +
        `To start earning money from your shortened links, I need to collect some information.\n\n` +
        `Please provide your mobile number (with country code):\n` +
        `Example: +**********`
      ).then(() => {
        console.log(`✅ Welcome message sent successfully to ${username}`);
      }).catch((error) => {
        console.error(`❌ Failed to send welcome message:`, error.message);
      });

      // Initialize user session
      this.userSessions.set(telegramId, {
        step: 'mobile',
        telegramId: telegramId,
        chatId: chatId,
        username: username
      });

      console.log(`📝 Session created for user ${username} (${telegramId})`);
    });

    // Dashboard command
    this.bot.onText(/\/dashboard/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`📊 /dashboard command received from @${username} (ID: ${telegramId})`);

      if (!telegramId) return;

      this.showDashboard(chatId, telegramId);
    });

    // Status command
    this.bot.onText(/\/status/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`ℹ️ /status command received from @${username} (ID: ${telegramId})`);

      if (!telegramId) return;

      const isRegistered = this.completedRegistrations.has(telegramId);

      if (isRegistered) {
        this.bot.sendMessage(chatId,
          `✅ Registration Status: COMPLETED\n\n` +
          `🎉 You're all set to start earning!\n` +
          `🔗 Visit: ${process.env.BASE_URL || 'http://localhost:5000'}\n\n` +
          `Use /dashboard to see your earnings or /start for main menu.`
        );
      } else {
        this.bot.sendMessage(chatId,
          `❌ Registration Status: NOT COMPLETED\n\n` +
          `📝 Use /start to complete your registration and start earning money from link clicks!`
        );
      }
    });

    // Search command for admins
    this.bot.onText(/\/search (.+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';
      const searchQuery = match?.[1];

      console.log(`🔍 /search command received from @${username}: "${searchQuery}"`);

      if (!telegramId || !searchQuery) return;

      // Check if user is admin (you can add admin user IDs here)
      const adminIds = ['YOUR_ADMIN_TELEGRAM_ID']; // Replace with actual admin IDs
      if (!adminIds.includes(telegramId) && chatId.toString() !== TEST_CHANNEL_ID) {
        this.bot.sendMessage(chatId, '❌ Access denied. This command is for admins only.');
        return;
      }

      try {
        await this.searchData(chatId, searchQuery);
      } catch (error) {
        console.error('Error in search command:', error);
        this.bot.sendMessage(chatId, '❌ Search failed. Please try again.');
      }
    });

    // Help command
    this.bot.onText(/\/help/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`❓ /help command received from @${username}`);

      const helpText = `
🤖 **Gaming Deals Tracker Bot Commands**

**User Commands:**
• \`/start\` - Begin registration or show main menu
• \`/dashboard\` - View your earnings and stats
• \`/status\` - Check your registration status
• \`/id\` - Get your Telegram ID for admin configuration
• \`/help\` - Show this help message

**Admin Commands:**
• \`/search <query>\` - Search for users, links, or withdrawals
• \`/testadmin\` - Test if you're configured as an admin
• Admin buttons for withdrawal approval/rejection sent privately

**Search Examples:**
• \`/search username\` - Find user by username
• \`/search @telegram\` - Find by telegram username
• \`/search withdrawal\` - Find withdrawal requests
• \`/search link_code\` - Find specific link

**Admin Features:**
• Withdrawal requests sent privately with action buttons
• Click buttons to approve/reject withdrawals instantly
• Search functionality for data management

**Tips:**
• Search is case-insensitive and supports partial matches
• Admins receive withdrawal notifications with interactive buttons

**Website:** ${process.env.BASE_URL || 'http://localhost:5000'}
      `.trim();

      this.bot.sendMessage(chatId, helpText, { parse_mode: 'Markdown' });
    });

    // ID command to help users get their Telegram ID
    this.bot.onText(/\/id/, (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';
      const firstName = msg.from?.first_name || '';
      const lastName = msg.from?.last_name || '';

      console.log(`🆔 /id command from @${username} (ID: ${telegramId})`);

      const idMessage = `🆔 **YOUR TELEGRAM INFORMATION**\n\n` +
        `👤 **Name:** ${firstName} ${lastName}`.trim() + `\n` +
        `📱 **Username:** @${username}\n` +
        `🔢 **User ID:** \`${telegramId}\`\n` +
        `💬 **Chat ID:** \`${chatId}\`\n\n` +
        `📋 **To make yourself an admin:**\n` +
        `1. Copy your User ID: \`${telegramId}\`\n` +
        `2. Update your .env file:\n` +
        `   \`TELEGRAM_ADMIN_IDS=${telegramId}\`\n` +
        `3. Restart your server\n` +
        `4. Test with a withdrawal request\n\n` +
        `✅ You'll then receive withdrawal requests with buttons!`;

      this.bot.sendMessage(chatId, idMessage, { parse_mode: 'Markdown' });
    });

    // Simple button test command
    this.bot.onText(/\/buttontest/, async (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`🧪 /buttontest command from @${username} (ID: ${telegramId})`);

      const testMessage = `🧪 **BUTTON TEST**\n\nClick the buttons below to test if they work:`;

      const testButtons = {
        inline_keyboard: [
          [
            { text: '✅ Test Button 1', callback_data: `test_button_1_${telegramId}` },
            { text: '❌ Test Button 2', callback_data: `test_button_2_${telegramId}` }
          ],
          [
            { text: '📞 Test Button 3', callback_data: `test_button_3_${telegramId}` },
            { text: '🔍 Test Button 4', callback_data: `test_button_4_${telegramId}` }
          ]
        ]
      };

      try {
        await this.bot.sendMessage(chatId, testMessage, {
          parse_mode: 'Markdown',
          reply_markup: testButtons
        });
        console.log(`✅ Test buttons sent to ${telegramId}`);
      } catch (error) {
        console.log(`❌ Failed to send test buttons:`, error.message);
        this.bot.sendMessage(chatId, `❌ Error sending test buttons: ${error.message}`);
      }
    });

    // Test admin command
    this.bot.onText(/\/testadmin/, async (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      const username = msg.from?.username || 'Unknown';

      console.log(`🧪 /testadmin command from @${username} (ID: ${telegramId})`);

      // Get admin IDs from environment
      const adminIdsEnv = process.env.TELEGRAM_ADMIN_IDS || '';
      const adminIds = adminIdsEnv ? adminIdsEnv.split(',').map(id => id.trim()) : [];

      const isAdmin = adminIds.includes(telegramId);

      if (isAdmin) {
        // Send a test message with buttons
        const testMessage = `🧪 **ADMIN TEST MESSAGE**\n\n` +
          `✅ You are configured as an admin!\n` +
          `🆔 Your ID: ${telegramId}\n` +
          `📋 All admin IDs: ${adminIds.join(', ')}\n\n` +
          `This is how withdrawal requests will look:`;

        const testButtons = {
          inline_keyboard: [
            [
              { text: '✅ Test Payment Done', callback_data: `test_done_${telegramId}` },
              { text: '❌ Test Reject', callback_data: `test_reject_${telegramId}` }
            ],
            [
              { text: '📞 Test Contact', callback_data: `test_contact_${telegramId}` },
              { text: '🔍 Test Details', callback_data: `test_details_${telegramId}` }
            ]
          ]
        };

        try {
          await this.bot.sendMessage(chatId, testMessage, {
            parse_mode: 'Markdown',
            reply_markup: testButtons
          });
          console.log(`✅ Test admin message sent to ${telegramId}`);
        } catch (error) {
          console.log(`❌ Failed to send test message:`, error.message);
          this.bot.sendMessage(chatId, `❌ Error sending test message: ${error.message}`);
        }
      } else {
        const notAdminMessage = `❌ **NOT AN ADMIN**\n\n` +
          `🆔 Your ID: ${telegramId}\n` +
          `📋 Configured admin IDs: ${adminIds.join(', ') || 'None configured'}\n\n` +
          `To become an admin:\n` +
          `1. Add your ID to .env: TELEGRAM_ADMIN_IDS=${telegramId}\n` +
          `2. Restart the server\n` +
          `3. Run /testadmin again`;

        this.bot.sendMessage(chatId, notAdminMessage, { parse_mode: 'Markdown' });
      }
    });

    // Handle text messages
    this.bot.on('message', async (msg) => {
      if (msg.text?.startsWith('/')) return; // Skip commands

      const telegramId = msg.from?.id.toString();
      const chatId = msg.chat.id;
      const username = msg.from?.username || 'Unknown';

      console.log(`💬 Message received from @${username}: "${msg.text}"`);

      if (!telegramId || !msg.text) {
        console.log('❌ Missing telegram ID or message text');
        return;
      }

      const session = this.userSessions.get(telegramId);
      if (!session) {
        console.log(`❌ No session found for user ${username}, sending start prompt`);
        this.bot.sendMessage(chatId, 'Please start with /start command');
        return;
      }

      console.log(`🔄 Processing message for user ${username} in step: ${session.step}`);
      await this.handleUserInput(telegramId, msg.text, chatId);
    });

    // Handle callback queries (inline keyboard buttons)
    this.bot.on('callback_query', async (query) => {
      const telegramId = query.from.id.toString();
      const chatId = query.message?.chat.id;
      const username = query.from.username || 'Unknown';

      console.log(`🔘 Callback query from @${username}: ${query.data}`);

      // Always answer the callback query first to remove loading state
      try {
        await this.bot.answerCallbackQuery(query.id, { text: 'Processing...' });
        console.log(`✅ Answered callback query for ${query.data}`);
      } catch (error) {
        console.log('⚠️ Failed to answer callback query:', error.message);
      }

      if (!chatId) {
        console.log('❌ No chat ID in callback query');
        return;
      }

      try {
        if (query.data?.startsWith('menu_')) {
          console.log(`🔧 Handling menu callback: ${query.data}`);
          await this.handleMenuCallback(telegramId, query.data, chatId, username);
        } else if (query.data?.startsWith('payment_') || query.data?.startsWith('contact_') || query.data?.startsWith('view_') || query.data?.startsWith('test_')) {
          console.log(`🔧 Handling admin withdrawal action: ${query.data}`);
          await this.handleAdminWithdrawalAction(query);
        } else if (query.data?.startsWith('test_button_')) {
          console.log(`🧪 Handling test button: ${query.data}`);
          await this.handleTestButtonClick(query);
        } else {
          console.log(`🔧 Handling other callback: ${query.data}`);
          await this.handleCallbackQuery(telegramId, query.data || '', chatId);
        }
      } catch (error) {
        console.error('❌ Error handling callback query:', error);
        try {
          this.bot.sendMessage(chatId, `❌ Error processing button: ${error.message}`);
        } catch (sendError) {
          console.error('❌ Failed to send error message:', sendError);
        }
      }
    });
  }

  private async handleUserInput(telegramId: string, text: string, chatId: number) {
    const session = this.userSessions.get(telegramId);
    if (!session) return;

    switch (session.step) {
      case 'awaiting_url':
        await this.handleUrlShortening(telegramId, text, chatId);
        break;
      case 'mobile':
        // Validate mobile number
        if (!/^\+\d{10,15}$/.test(text)) {
          this.bot.sendMessage(chatId, 
            '❌ Invalid mobile number format. Please provide a valid number with country code.\n' +
            'Example: +**********'
          );
          return;
        }

        session.mobileNumber = text;
        session.step = 'withdrawal';

        // Show withdrawal method options
        const withdrawalKeyboard = {
          inline_keyboard: [
            [{ text: '🇮🇳 UPI ID', callback_data: 'withdrawal_upi' }],
            [{ text: '💳 PayPal', callback_data: 'withdrawal_paypal' }],
            [{ text: '🏦 Bank Transfer', callback_data: 'withdrawal_bank' }],
            [{ text: '💰 Skrill', callback_data: 'withdrawal_skrill' }]
          ]
        };

        this.bot.sendMessage(chatId, 
          `✅ Mobile number saved: ${text}\n\n` +
          `Now, please select your preferred withdrawal method:`,
          { reply_markup: withdrawalKeyboard }
        );
        break;

      case 'withdrawal_details':
        session.withdrawalDetails = text;
        await this.completeRegistration(telegramId, chatId);
        break;
    }

    this.userSessions.set(telegramId, session);
  }

  private async handleCallbackQuery(telegramId: string, data: string, chatId: number) {
    const session = this.userSessions.get(telegramId);

    if (data === 'menu_back') {
      // Clear any active session and show main menu
      this.userSessions.delete(telegramId);
      const username = 'User'; // We could store this better
      this.showMainMenu(chatId, username);
      return;
    }

    if (!session) return;

    if (data.startsWith('withdrawal_')) {
      const method = data.replace('withdrawal_', '');
      session.withdrawalMethod = method;
      session.step = 'withdrawal_details';

      let promptText = '';
      switch (method) {
        case 'upi':
          promptText = '🇮🇳 Please provide your UPI ID (e.g., yourname@paytm):';
          break;
        case 'paypal':
          promptText = '💳 Please provide your PayPal email address:';
          break;
        case 'bank':
          promptText = '🏦 Please provide your bank account details (Account Number, Bank Name, IFSC):';
          break;
        case 'skrill':
          promptText = '💰 Please provide your Skrill email address:';
          break;
      }

      this.bot.sendMessage(chatId, promptText);
      this.userSessions.set(telegramId, session);
    }
  }

  private async handleUrlShortening(telegramId: string, url: string, chatId: number) {
    try {
      // Validate URL
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        this.bot.sendMessage(chatId,
          '❌ Invalid URL format. Please provide a valid URL starting with http:// or https://'
        );
        return;
      }

      // Call the API to shorten the link
      const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
      const response = await fetch(`${baseUrl}/api/links/shorten`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ originalUrl: url })
      });

      if (response.ok) {
        const data = await response.json();

        const successKeyboard = {
          inline_keyboard: [
            [{ text: '🔗 Shorten Another', callback_data: 'menu_shorten' }],
            [{ text: '🔙 Back to Menu', callback_data: 'menu_back' }]
          ]
        };

        this.bot.sendMessage(chatId,
          `✅ Link shortened successfully!\n\n` +
          `🔗 Short URL: ${data.shortUrl}\n` +
          `📊 Original: ${data.originalUrl}\n\n` +
          `💡 Share this link to start earning $0.007 per click!`,
          { reply_markup: successKeyboard }
        );
      } else {
        this.bot.sendMessage(chatId,
          '❌ Failed to shorten link. Please try again or contact support.'
        );
      }

      // Clear the session
      this.userSessions.delete(telegramId);

    } catch (error) {
      console.error('Error shortening URL:', error);
      this.bot.sendMessage(chatId,
        '❌ Error shortening link. Please try again later.'
      );
      this.userSessions.delete(telegramId);
    }
  }

  private async completeRegistration(telegramId: string, chatId: number) {
    const session = this.userSessions.get(telegramId);
    if (!session) return;

    try {
      // Save registration data to test channel
      const registrationData = {
        type: 'telegram_registration',
        telegramId: session.telegramId,
        mobileNumber: session.mobileNumber,
        withdrawalMethod: session.withdrawalMethod,
        withdrawalDetails: session.withdrawalDetails,
        registeredAt: new Date().toISOString()
      };

      // Send to test channel
      await this.bot.sendMessage(TEST_CHANNEL_ID, JSON.stringify(registrationData, null, 2));

      // Send success message to user
      this.bot.sendMessage(chatId, 
        `🎉 Registration completed successfully!\n\n` +
        `📱 Mobile: ${session.mobileNumber}\n` +
        `💳 Withdrawal: ${session.withdrawalMethod}\n` +
        `📝 Details: ${session.withdrawalDetails}\n\n` +
        `✅ Your account is now set up for earning!\n\n` +
        `💰 You'll earn $0.007 for each click on your shortened links\n` +
        `💵 Minimum withdrawal: $4.00\n\n` +
        `🔗 Start creating links at: ${process.env.BASE_URL || 'your-app-url'}\n\n` +
        `Use /dashboard to check your earnings anytime!\n\n` +
        `📱 Bot: @DileVdbot`
      );

      // Mark registration as completed
      this.completedRegistrations.add(telegramId);

      // Save to Telegram for persistence
      try {
        const completedMessage = JSON.stringify({
          type: 'registration_completed',
          telegramId: telegramId,
          username: session.username,
          completedAt: new Date().toISOString()
        });

        await this.bot.sendMessage(TEST_CHANNEL_ID, completedMessage);
        console.log(`💾 Registration completion saved to Telegram for ${telegramId}`);
      } catch (error) {
        console.error('Error saving registration completion:', error);
      }

      // Clean up session
      this.userSessions.delete(telegramId);

      console.log(`✅ Telegram registration completed for user ${telegramId}`);
    } catch (error) {
      console.error('Error completing registration:', error);
      this.bot.sendMessage(chatId,
        '❌ Registration failed. Please try again later or contact support.'
      );
    }
  }

  private showMainMenu(chatId: number, username: string) {
    const menuKeyboard = {
      inline_keyboard: [
        [{ text: '📊 Dashboard', callback_data: 'menu_dashboard' }],
        [{ text: '🔗 Shorten Link', callback_data: 'menu_shorten' }],
        [{ text: '🏠 Go to Website', url: process.env.BASE_URL || 'http://localhost:5000' }]
      ]
    };

    this.bot.sendMessage(chatId,
      `🎉 Welcome back, ${username}!\n\n` +
      `Your registration is complete. What would you like to do?`,
      { reply_markup: menuKeyboard }
    );
  }

  private async handleMenuCallback(telegramId: string, data: string, chatId: number, username: string) {
    switch (data) {
      case 'menu_dashboard':
        await this.showDashboard(chatId, telegramId);
        break;
      case 'menu_shorten':
        await this.startLinkShortening(chatId, telegramId);
        break;
    }
  }

  private async showDashboard(chatId: number, telegramId: string) {
    try {
      // Try to find user data by telegramId
      // This is a simplified approach - in production you'd have a proper user lookup
      const backKeyboard = {
        inline_keyboard: [
          [{ text: '🔙 Back to Menu', callback_data: 'menu_back' }],
          [{ text: '🏠 Go to Website', url: process.env.BASE_URL || 'http://localhost:5000' }]
        ]
      };

      this.bot.sendMessage(chatId,
        `📊 Your Earnings Dashboard\n\n` +
        `💰 Total Earnings: $0.000\n` +
        `💵 Available Balance: $0.000\n` +
        `🔗 Total Links: 0\n` +
        `👆 Total Clicks: 0\n\n` +
        `💡 Create your first link on the website to start earning!\n\n` +
        `🎯 How it works:\n` +
        `• Register/Login on website\n` +
        `• Create shortened links\n` +
        `• Earn $0.007 per click\n` +
        `• Withdraw at $4.00 minimum\n\n` +
        `📱 Payment methods: UPI, PayPal, Bank, Skrill`,
        { reply_markup: backKeyboard }
      );
    } catch (error) {
      console.error('Error showing dashboard:', error);
      this.bot.sendMessage(chatId, '❌ Error loading dashboard. Please try again.');
    }
  }

  private async startLinkShortening(chatId: number, telegramId: string) {
    // Set user in link shortening mode
    this.userSessions.set(telegramId, {
      step: 'awaiting_url',
      telegramId: telegramId,
      chatId: chatId
    });

    const cancelKeyboard = {
      inline_keyboard: [
        [{ text: '❌ Cancel', callback_data: 'menu_back' }]
      ]
    };

    this.bot.sendMessage(chatId,
      `🔗 Link Shortener\n\n` +
      `Please send me the URL you want to shorten:\n` +
      `Example: https://example.com`,
      { reply_markup: cancelKeyboard }
    );
  }

  // Dashboard command
  setupDashboardCommand() {
    this.bot.onText(/\/dashboard/, async (msg) => {
      const chatId = msg.chat.id;
      const telegramId = msg.from?.id.toString();
      
      if (!telegramId) return;

      // This would typically fetch user data from storage
      // For now, show a placeholder message
      this.bot.sendMessage(chatId, 
        `📊 Your Earnings Dashboard\n\n` +
        `💰 Total Earnings: $0.000\n` +
        `💵 Available Balance: $0.000\n` +
        `🔗 Total Links: 0\n` +
        `👆 Total Clicks: 0\n\n` +
        `Create your first link at: ${process.env.BASE_URL || 'your-app-url'}`
      );
    });
  }

  // Method to send earnings update to user
  async notifyEarningsUpdate(telegramId: string, earnings: number, totalEarnings: number) {
    try {
      this.bot.sendMessage(parseInt(telegramId),
        `💰 New Earnings!\n\n` +
        `+$${earnings.toFixed(3)} from a link click\n` +
        `Total Earnings: $${totalEarnings.toFixed(3)}\n\n` +
        `Keep sharing your links to earn more! 🚀`
      );
    } catch (error) {
      console.error('Error sending earnings notification:', error);
    }
  }

  // Search functionality for admins
  private async searchData(chatId: number, query: string) {
    try {
      console.log(`🔍 Searching for: "${query}"`);

      // Search through different data types
      const results = {
        users: [] as any[],
        links: [] as any[],
        withdrawals: [] as any[],
        registrations: [] as any[]
      };

      // Get all users and search through them
      const users = await storage.getAllUsers();
      for (const user of users) {
        if (this.matchesQuery(user, query)) {
          results.users.push(user);
        }
      }

      // Get all links and search through them
      const links = await storage.getAllLinks();
      for (const link of links) {
        if (this.matchesQuery(link, query)) {
          results.links.push(link);
        }
      }

      // Format and send results
      await this.sendSearchResults(chatId, query, results);

    } catch (error) {
      console.error('Error searching data:', error);
      this.bot.sendMessage(chatId, '❌ Search failed. Please try again.');
    }
  }

  private matchesQuery(obj: any, query: string): boolean {
    const searchQuery = query.toLowerCase();
    const objString = JSON.stringify(obj).toLowerCase();

    // Check if query matches any field in the object
    return objString.includes(searchQuery) ||
           (obj.username && obj.username.toLowerCase().includes(searchQuery)) ||
           (obj.fullName && obj.fullName.toLowerCase().includes(searchQuery)) ||
           (obj.telegramUsername && obj.telegramUsername.toLowerCase().includes(searchQuery)) ||
           (obj.paymentDetails && obj.paymentDetails.toLowerCase().includes(searchQuery)) ||
           (obj.shortCode && obj.shortCode.toLowerCase().includes(searchQuery)) ||
           (obj.originalUrl && obj.originalUrl.toLowerCase().includes(searchQuery));
  }

  private async sendSearchResults(chatId: number, query: string, results: any) {
    const totalResults = results.users.length + results.links.length + results.withdrawals.length;

    if (totalResults === 0) {
      this.bot.sendMessage(chatId,
        `🔍 **Search Results for "${query}"**\n\n` +
        `❌ No results found.\n\n` +
        `Try searching with different keywords or partial matches.`,
        { parse_mode: 'Markdown' }
      );
      return;
    }

    let message = `🔍 **Search Results for "${query}"**\n\n`;
    message += `📊 **Summary:** ${totalResults} results found\n\n`;

    // Show user results
    if (results.users.length > 0) {
      message += `👥 **Users (${results.users.length}):**\n`;
      for (const user of results.users.slice(0, 5)) { // Limit to 5 results
        message += `• **${user.fullName || user.username}** (@${user.username})\n`;
        message += `  💰 Balance: $${user.availableBalance?.toFixed(3) || '0.000'}\n`;
        message += `  📱 Telegram: ${user.telegramUsername || 'N/A'}\n`;
        message += `  💳 Payment: ${user.paymentMethod || 'Not set'}\n\n`;
      }
      if (results.users.length > 5) {
        message += `... and ${results.users.length - 5} more users\n\n`;
      }
    }

    // Show link results
    if (results.links.length > 0) {
      message += `🔗 **Links (${results.links.length}):**\n`;
      for (const link of results.links.slice(0, 5)) { // Limit to 5 results
        message += `• **${link.shortCode}** → ${link.originalUrl}\n`;
        message += `  👆 Clicks: ${link.clicks || 0}\n`;
        message += `  💰 Earnings: $${link.earnings?.toFixed(3) || '0.000'}\n`;
        message += `  👤 Owner: ${link.userId}\n\n`;
      }
      if (results.links.length > 5) {
        message += `... and ${results.links.length - 5} more links\n\n`;
      }
    }

    // Show withdrawal results
    if (results.withdrawals.length > 0) {
      message += `💸 **Withdrawals (${results.withdrawals.length}):**\n`;
      for (const withdrawal of results.withdrawals.slice(0, 3)) { // Limit to 3 results
        message += `• **$${withdrawal.amount?.toFixed(3)}** via ${withdrawal.method}\n`;
        message += `  📅 ${new Date(withdrawal.requestedAt).toLocaleDateString()}\n`;
        message += `  ⚡ Status: ${withdrawal.status}\n\n`;
      }
      if (results.withdrawals.length > 3) {
        message += `... and ${results.withdrawals.length - 3} more withdrawals\n\n`;
      }
    }

    message += `💡 **Tip:** Use more specific keywords for better results.`;

    // Split message if too long
    if (message.length > 4000) {
      const parts = this.splitMessage(message, 4000);
      for (const part of parts) {
        await this.bot.sendMessage(chatId, part, { parse_mode: 'Markdown' });
      }
    } else {
      this.bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
    }
  }

  private splitMessage(text: string, maxLength: number): string[] {
    const parts = [];
    let currentPart = '';

    const lines = text.split('\n');
    for (const line of lines) {
      if ((currentPart + line + '\n').length > maxLength) {
        if (currentPart) {
          parts.push(currentPart.trim());
          currentPart = '';
        }
      }
      currentPart += line + '\n';
    }

    if (currentPart.trim()) {
      parts.push(currentPart.trim());
    }

    return parts;
  }

  // Handle admin withdrawal action buttons
  private async handleAdminWithdrawalAction(query: any) {
    try {
      const chatId = query.message?.chat.id;
      const messageId = query.message?.message_id;
      const data = query.data;
      const adminUsername = query.from.username || 'Unknown Admin';

      console.log(`🔧 Admin action by @${adminUsername}: ${data}`);

      if (!chatId || !data) return;

      const parts = data.split('_');
      const action = parts[0] + '_' + parts[1]; // payment_done, payment_reject, etc.
      const withdrawalId = parts[2];
      const userId = parts[3];

      console.log(`🔧 Processing admin action: ${action}, withdrawal: ${withdrawalId}, user: ${userId}`);

      switch (action) {
        case 'payment_done':
          await this.processPaymentDone(withdrawalId, userId, chatId, messageId, adminUsername);
          break;
        case 'payment_reject':
          await this.processPaymentReject(withdrawalId, userId, chatId, messageId, adminUsername);
          break;
        case 'contact_user':
          await this.contactUser(withdrawalId, userId, chatId, adminUsername);
          break;
        case 'view_user':
          await this.viewUserDetails(withdrawalId, userId, chatId, adminUsername);
          break;
        case 'test_done':
        case 'test_reject':
        case 'test_contact':
        case 'test_details':
          await this.handleTestButton(action, chatId, adminUsername);
          break;
        default:
          console.log(`❌ Unknown admin action: ${action}`);
          this.bot.sendMessage(chatId, `❌ Unknown action: ${action}`);
      }

      // Send final confirmation
      try {
        await this.bot.answerCallbackQuery(query.id, { text: 'Action completed!' });
      } catch (answerError) {
        console.log('⚠️ Failed to send final callback answer:', answerError.message);
      }
    } catch (error) {
      console.error('Error handling admin withdrawal action:', error);
      try {
        await this.bot.answerCallbackQuery(query.id, { text: 'Error processing action' });
      } catch (answerError) {
        console.log('⚠️ Failed to send error callback answer:', answerError.message);
      }
    }
  }

  private async processPaymentDone(withdrawalId: string, userId: string, chatId: number, messageId: number, adminUsername: string) {
    try {
      console.log(`✅ Processing payment done for withdrawal ${withdrawalId}, user ${userId}`);

      // Get user details first
      const user = await storage.getUserById(parseInt(userId));
      if (!user) {
        this.bot.sendMessage(chatId, `❌ User ${userId} not found`);
        return;
      }

      // Update withdrawal status in storage
      await storage.updateWithdrawalStatus(parseInt(withdrawalId), 'completed', adminUsername);

      // Update the message to show payment completed
      const updatedMessage = `✅ **PAYMENT COMPLETED**\n\n` +
        `💰 Withdrawal ID: ${withdrawalId}\n` +
        `👤 User: @${user.username} (${user.fullName || 'No name'})\n` +
        `👨‍💼 Processed by: @${adminUsername}\n` +
        `⏰ Completed at: ${new Date().toLocaleString()}\n\n` +
        `✅ Payment has been sent to the user.\n` +
        `💳 Payment method: ${user.paymentMethod || 'Not set'}\n` +
        `📧 Payment details: ${user.paymentDetails || 'Not provided'}`;

      await this.bot.editMessageText(updatedMessage, {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      });

      // Send confirmation message
      this.bot.sendMessage(chatId, `✅ Payment completed for withdrawal ${withdrawalId}!`);
      console.log(`✅ Payment marked as done by @${adminUsername} for withdrawal ${withdrawalId}`);
    } catch (error) {
      console.error('Error processing payment done:', error);
      this.bot.sendMessage(chatId, `❌ Error processing payment: ${error.message}`);
    }
  }

  private async processPaymentReject(withdrawalId: string, userId: string, chatId: number, messageId: number, adminUsername: string) {
    try {
      console.log(`❌ Processing payment rejection for withdrawal ${withdrawalId}, user ${userId}`);

      // Get user details first
      const user = await storage.getUserById(parseInt(userId));
      if (!user) {
        this.bot.sendMessage(chatId, `❌ User ${userId} not found`);
        return;
      }

      // Update withdrawal status in storage
      await storage.updateWithdrawalStatus(parseInt(withdrawalId), 'rejected', adminUsername);

      // NOTE: As requested, we still deduct money even when rejecting
      // (Money stays deducted, no balance restoration)

      // Update the message to show payment rejected
      const updatedMessage = `❌ **PAYMENT REJECTED**\n\n` +
        `💰 Withdrawal ID: ${withdrawalId}\n` +
        `👤 User: @${user.username} (${user.fullName || 'No name'})\n` +
        `👨‍💼 Rejected by: @${adminUsername}\n` +
        `⏰ Rejected at: ${new Date().toLocaleString()}\n\n` +
        `❌ Payment has been rejected.\n` +
        `💰 Money remains deducted from user account.\n` +
        `📝 Reason: Admin rejection`;

      await this.bot.editMessageText(updatedMessage, {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown'
      });

      // Send confirmation message
      this.bot.sendMessage(chatId, `❌ Payment rejected for withdrawal ${withdrawalId}. Money remains deducted.`);
      console.log(`❌ Payment rejected by @${adminUsername} for withdrawal ${withdrawalId} - money stays deducted`);
    } catch (error) {
      console.error('Error processing payment reject:', error);
      this.bot.sendMessage(chatId, `❌ Error processing rejection: ${error.message}`);
    }
  }

  private async contactUser(withdrawalId: string, userId: string, chatId: number, adminUsername: string) {
    try {
      console.log(`📞 Getting contact info for user ${userId}, withdrawal ${withdrawalId}`);

      const user = await storage.getUserById(parseInt(userId));
      if (!user) {
        this.bot.sendMessage(chatId, '❌ User not found');
        return;
      }

      const contactInfo = `📞 **USER CONTACT INFORMATION**\n\n` +
        `💰 **Withdrawal ID:** ${withdrawalId}\n` +
        `👤 **Name:** ${user.fullName || 'Not provided'}\n` +
        `🆔 **Username:** @${user.username}\n` +
        `📱 **Telegram:** ${user.telegramUsername || 'Not provided'}\n` +
        `📞 **Phone:** ${user.phoneNumber || 'Not provided'}\n\n` +
        `💳 **Payment Information:**\n` +
        `• **Method:** ${user.paymentMethod || 'Not set'}\n` +
        `• **Details:** ${user.paymentDetails || 'Not provided'}\n\n` +
        `📍 **Address:**\n${user.address || 'Not provided'}\n\n` +
        `💰 **Account Info:**\n` +
        `• **Total Earnings:** $${user.totalEarnings?.toFixed(3) || '0.000'}\n` +
        `• **Current Balance:** $${user.availableBalance?.toFixed(3) || '0.000'}\n\n` +
        `💡 **Contact Methods:**\n` +
        `• Telegram: ${user.telegramUsername || 'Not available'}\n` +
        `• Phone: ${user.phoneNumber || 'Not available'}\n` +
        `• Through website dashboard`;

      this.bot.sendMessage(chatId, contactInfo, { parse_mode: 'Markdown' });
      console.log(`📞 Contact info sent for user ${user.username}`);
    } catch (error) {
      console.error('Error getting contact info:', error);
      this.bot.sendMessage(chatId, `❌ Error getting contact info: ${error.message}`);
    }
  }

  private async viewUserDetails(withdrawalId: string, userId: string, chatId: number, adminUsername: string) {
    try {
      console.log(`🔍 Getting detailed user info for user ${userId}, withdrawal ${withdrawalId}`);

      const user = await storage.getUserById(parseInt(userId));
      if (!user) {
        this.bot.sendMessage(chatId, '❌ User not found');
        return;
      }

      // Get user's withdrawal history
      const withdrawals = await storage.getUserWithdrawals(parseInt(userId));
      const totalWithdrawals = withdrawals.length;
      const completedWithdrawals = withdrawals.filter(w => w.status === 'completed').length;

      const userDetails = `👤 **COMPLETE USER PROFILE**\n\n` +
        `💰 **Withdrawal ID:** ${withdrawalId}\n\n` +
        `**👤 Personal Information:**\n` +
        `• **Full Name:** ${user.fullName || 'Not provided'}\n` +
        `• **Username:** @${user.username}\n` +
        `• **Telegram:** ${user.telegramUsername || 'Not provided'}\n` +
        `• **Phone:** ${user.phoneNumber || 'Not provided'}\n\n` +
        `**📍 Address:**\n${user.address || 'Not provided'}\n\n` +
        `**💳 Payment Information:**\n` +
        `• **Method:** ${user.paymentMethod || 'Not set'}\n` +
        `• **Details:** ${user.paymentDetails || 'Not provided'}\n\n` +
        `**📊 Account Statistics:**\n` +
        `• **Total Earnings:** $${user.totalEarnings?.toFixed(3) || '0.000'}\n` +
        `• **Available Balance:** $${user.availableBalance?.toFixed(3) || '0.000'}\n` +
        `• **Total Links Created:** ${user.totalLinks || 0}\n` +
        `• **Total Clicks Received:** ${user.totalClicks || 0}\n` +
        `• **Total Withdrawals:** ${totalWithdrawals}\n` +
        `• **Completed Withdrawals:** ${completedWithdrawals}\n\n` +
        `**🌐 Traffic Sources:**\n${user.trafficSource || 'Not provided'}\n\n` +
        `**🔗 Traffic Links:**\n${user.trafficSourceLinks || 'Not provided'}\n\n` +
        `**📅 Account Created:** ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}\n` +
        `**🔄 Last Updated:** ${new Date().toLocaleString()}`;

      this.bot.sendMessage(chatId, userDetails, { parse_mode: 'Markdown' });
      console.log(`🔍 Detailed user info sent for ${user.username}`);
    } catch (error) {
      console.error('Error getting user details:', error);
      this.bot.sendMessage(chatId, `❌ Error getting user details: ${error.message}`);
    }
  }

  private async handleTestButton(action: string, chatId: number, adminUsername: string) {
    try {
      let message = '';
      switch (action) {
        case 'test_done':
          message = '✅ **Test Payment Done Button Clicked!**\n\nThis would mark a real withdrawal as completed.';
          break;
        case 'test_reject':
          message = '❌ **Test Reject Button Clicked!**\n\nThis would reject a real withdrawal (money stays deducted).';
          break;
        case 'test_contact':
          message = '📞 **Test Contact Button Clicked!**\n\nThis would show real user contact information.';
          break;
        case 'test_details':
          message = '🔍 **Test Details Button Clicked!**\n\nThis would show complete user profile and address.';
          break;
      }

      this.bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Error handling test button:', error);
    }
  }

  private async handleTestButtonClick(query: any) {
    try {
      const chatId = query.message?.chat.id;
      const data = query.data;
      const username = query.from.username || 'Unknown';

      console.log(`🧪 Test button clicked by @${username}: ${data}`);

      if (!chatId) return;

      const parts = data.split('_');
      const buttonNumber = parts[2];

      let message = '';
      switch (buttonNumber) {
        case '1':
          message = '✅ **Test Button 1 Clicked!**\n\nThis button is working correctly!';
          break;
        case '2':
          message = '❌ **Test Button 2 Clicked!**\n\nThis button is also working!';
          break;
        case '3':
          message = '📞 **Test Button 3 Clicked!**\n\nAll buttons are responding!';
          break;
        case '4':
          message = '🔍 **Test Button 4 Clicked!**\n\nCallback system is functional!';
          break;
        default:
          message = '🧪 **Unknown Test Button**\n\nButton system is working!';
      }

      await this.bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
      console.log(`✅ Test button response sent for button ${buttonNumber}`);
    } catch (error) {
      console.error('Error handling test button click:', error);
    }
  }
}

// Export singleton instance
export const registrationBot = new UserRegistrationBot();
