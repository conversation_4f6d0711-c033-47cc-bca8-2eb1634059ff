// Quick test to verify the system is working
async function quickTest() {
  try {
    console.log('🧪 Quick System Test...\n');
    
    // Test 1: Check if server is responding
    console.log('1. Testing server health...');
    const healthResponse = await fetch('http://localhost:5000/');
    console.log('✅ Server responding:', healthResponse.ok ? 'Yes' : 'No');
    
    // Test 2: Register a new user
    console.log('\n2. Testing user registration...');
    const username = 'testuser' + Date.now();
    const registerResponse = await fetch('http://localhost:5000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password: 'testpass123' })
    });
    
    const registerData = await registerResponse.json();
    console.log('✅ Registration:', registerData.message);
    
    if (registerData.token) {
      console.log('🔑 Token received: Yes');
      
      // Test 3: Test payment details
      console.log('\n3. Testing payment details...');
      const paymentResponse = await fetch('http://localhost:5000/api/user/payment-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${registerData.token}`
        },
        body: JSON.stringify({
          fullName: 'Test User Complete',
          address: '123 Complete Address Street, Test City, TC 12345, USA',
          phoneNumber: '+1234567890',
          telegramUsername: '@testuser',
          trafficSource: 'Testing the complete system functionality',
          trafficSourceLinks: '• YouTube: https://youtube.com/@testuser\n• Website: https://testuser.com',
          paymentMethod: 'upi',
          paymentDetails: 'testuser@paytm'
        })
      });
      
      const paymentData = await paymentResponse.json();
      console.log('✅ Payment details:', paymentData.message);
      
      // Test 4: Create a link
      console.log('\n4. Testing link creation...');
      const linkResponse = await fetch('http://localhost:5000/api/links/shorten', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${registerData.token}`
        },
        body: JSON.stringify({ originalUrl: 'https://github.com' })
      });
      
      const linkData = await linkResponse.json();
      console.log('✅ Link created:', linkData.shortCode);
      console.log('🔗 Short URL:', linkData.shortUrl);
      
      console.log('\n🎉 All backend tests passed! System is ready!');
    } else {
      console.log('❌ No token received');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

quickTest();
