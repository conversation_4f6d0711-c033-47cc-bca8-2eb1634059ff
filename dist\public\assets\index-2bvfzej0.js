var kc=e=>{throw TypeError(e)};var nl=(e,t,n)=>t.has(e)||kc("Cannot "+n);var S=(e,t,n)=>(nl(e,t,"read from private field"),n?n.call(e):t.get(e)),z=(e,t,n)=>t.has(e)?kc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),A=(e,t,n,r)=>(nl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),B=(e,t,n)=>(nl(e,t,"access private method"),n);var po=(e,t,n,r)=>({set _(s){A(e,t,s,n)},get _(){return S(e,t,r)}});function $0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const o=Object.getOwnPropertyDescriptor(r,s);o&&Object.defineProperty(e,s,o.get?o:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function Ef(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Pf={exports:{}},Ri={},Tf={exports:{}},Q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var so=Symbol.for("react.element"),V0=Symbol.for("react.portal"),B0=Symbol.for("react.fragment"),H0=Symbol.for("react.strict_mode"),W0=Symbol.for("react.profiler"),Q0=Symbol.for("react.provider"),G0=Symbol.for("react.context"),K0=Symbol.for("react.forward_ref"),q0=Symbol.for("react.suspense"),Y0=Symbol.for("react.memo"),X0=Symbol.for("react.lazy"),jc=Symbol.iterator;function Z0(e){return e===null||typeof e!="object"?null:(e=jc&&e[jc]||e["@@iterator"],typeof e=="function"?e:null)}var Rf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Of=Object.assign,Lf={};function Jr(e,t,n){this.props=e,this.context=t,this.refs=Lf,this.updater=n||Rf}Jr.prototype.isReactComponent={};Jr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function _f(){}_f.prototype=Jr.prototype;function Ja(e,t,n){this.props=e,this.context=t,this.refs=Lf,this.updater=n||Rf}var eu=Ja.prototype=new _f;eu.constructor=Ja;Of(eu,Jr.prototype);eu.isPureReactComponent=!0;var Cc=Array.isArray,Mf=Object.prototype.hasOwnProperty,tu={current:null},Af={key:!0,ref:!0,__self:!0,__source:!0};function Df(e,t,n){var r,s={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Mf.call(t,r)&&!Af.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:so,type:e,key:o,ref:i,props:s,_owner:tu.current}}function J0(e,t){return{$$typeof:so,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function nu(e){return typeof e=="object"&&e!==null&&e.$$typeof===so}function eg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ec=/\/+/g;function rl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?eg(""+e.key):t.toString(36)}function Do(e,t,n,r,s){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case so:case V0:i=!0}}if(i)return i=e,s=s(i),e=r===""?"."+rl(i,0):r,Cc(s)?(n="",e!=null&&(n=e.replace(Ec,"$&/")+"/"),Do(s,t,n,"",function(c){return c})):s!=null&&(nu(s)&&(s=J0(s,n+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(Ec,"$&/")+"/")+e)),t.push(s)),1;if(i=0,r=r===""?".":r+":",Cc(e))for(var a=0;a<e.length;a++){o=e[a];var u=r+rl(o,a);i+=Do(o,t,n,u,s)}else if(u=Z0(e),typeof u=="function")for(e=u.call(e),a=0;!(o=e.next()).done;)o=o.value,u=r+rl(o,a++),i+=Do(o,t,n,u,s);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function mo(e,t,n){if(e==null)return e;var r=[],s=0;return Do(e,r,"","",function(o){return t.call(n,o,s++)}),r}function tg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Re={current:null},Io={transition:null},ng={ReactCurrentDispatcher:Re,ReactCurrentBatchConfig:Io,ReactCurrentOwner:tu};function If(){throw Error("act(...) is not supported in production builds of React.")}Q.Children={map:mo,forEach:function(e,t,n){mo(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return mo(e,function(){t++}),t},toArray:function(e){return mo(e,function(t){return t})||[]},only:function(e){if(!nu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Q.Component=Jr;Q.Fragment=B0;Q.Profiler=W0;Q.PureComponent=Ja;Q.StrictMode=H0;Q.Suspense=q0;Q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ng;Q.act=If;Q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Of({},e.props),s=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=tu.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)Mf.call(t,u)&&!Af.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:so,type:e.type,key:s,ref:o,props:r,_owner:i}};Q.createContext=function(e){return e={$$typeof:G0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Q0,_context:e},e.Consumer=e};Q.createElement=Df;Q.createFactory=function(e){var t=Df.bind(null,e);return t.type=e,t};Q.createRef=function(){return{current:null}};Q.forwardRef=function(e){return{$$typeof:K0,render:e}};Q.isValidElement=nu;Q.lazy=function(e){return{$$typeof:X0,_payload:{_status:-1,_result:e},_init:tg}};Q.memo=function(e,t){return{$$typeof:Y0,type:e,compare:t===void 0?null:t}};Q.startTransition=function(e){var t=Io.transition;Io.transition={};try{e()}finally{Io.transition=t}};Q.unstable_act=If;Q.useCallback=function(e,t){return Re.current.useCallback(e,t)};Q.useContext=function(e){return Re.current.useContext(e)};Q.useDebugValue=function(){};Q.useDeferredValue=function(e){return Re.current.useDeferredValue(e)};Q.useEffect=function(e,t){return Re.current.useEffect(e,t)};Q.useId=function(){return Re.current.useId()};Q.useImperativeHandle=function(e,t,n){return Re.current.useImperativeHandle(e,t,n)};Q.useInsertionEffect=function(e,t){return Re.current.useInsertionEffect(e,t)};Q.useLayoutEffect=function(e,t){return Re.current.useLayoutEffect(e,t)};Q.useMemo=function(e,t){return Re.current.useMemo(e,t)};Q.useReducer=function(e,t,n){return Re.current.useReducer(e,t,n)};Q.useRef=function(e){return Re.current.useRef(e)};Q.useState=function(e){return Re.current.useState(e)};Q.useSyncExternalStore=function(e,t,n){return Re.current.useSyncExternalStore(e,t,n)};Q.useTransition=function(){return Re.current.useTransition()};Q.version="18.3.1";Tf.exports=Q;var w=Tf.exports;const Zt=Ef(w),rg=$0({__proto__:null,default:Zt},[w]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sg=w,og=Symbol.for("react.element"),ig=Symbol.for("react.fragment"),lg=Object.prototype.hasOwnProperty,ag=sg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ug={key:!0,ref:!0,__self:!0,__source:!0};function Ff(e,t,n){var r,s={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)lg.call(t,r)&&!ug.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:og,type:e,key:o,ref:i,props:s,_owner:ag.current}}Ri.Fragment=ig;Ri.jsx=Ff;Ri.jsxs=Ff;Pf.exports=Ri;var l=Pf.exports,zf={exports:{}},Ke={},Uf={exports:{}},$f={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,M){var F=P.length;P.push(M);e:for(;0<F;){var W=F-1>>>1,re=P[W];if(0<s(re,M))P[W]=M,P[F]=re,F=W;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var M=P[0],F=P.pop();if(F!==M){P[0]=F;e:for(var W=0,re=P.length,ot=re>>>1;W<ot;){var Ye=2*(W+1)-1,is=P[Ye],Ot=Ye+1,_n=P[Ot];if(0>s(is,F))Ot<re&&0>s(_n,is)?(P[W]=_n,P[Ot]=F,W=Ot):(P[W]=is,P[Ye]=F,W=Ye);else if(Ot<re&&0>s(_n,F))P[W]=_n,P[Ot]=F,W=Ot;else break e}}return M}function s(P,M){var F=P.sortIndex-M.sortIndex;return F!==0?F:P.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var u=[],c=[],d=1,f=null,m=3,y=!1,v=!1,x=!1,b=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(P){for(var M=n(c);M!==null;){if(M.callback===null)r(c);else if(M.startTime<=P)r(c),M.sortIndex=M.expirationTime,t(u,M);else break;M=n(c)}}function N(P){if(x=!1,h(P),!v)if(n(u)!==null)v=!0,$(k);else{var M=n(c);M!==null&&G(N,M.startTime-P)}}function k(P,M){v=!1,x&&(x=!1,g(E),E=-1),y=!0;var F=m;try{for(h(M),f=n(u);f!==null&&(!(f.expirationTime>M)||P&&!U());){var W=f.callback;if(typeof W=="function"){f.callback=null,m=f.priorityLevel;var re=W(f.expirationTime<=M);M=e.unstable_now(),typeof re=="function"?f.callback=re:f===n(u)&&r(u),h(M)}else r(u);f=n(u)}if(f!==null)var ot=!0;else{var Ye=n(c);Ye!==null&&G(N,Ye.startTime-M),ot=!1}return ot}finally{f=null,m=F,y=!1}}var C=!1,j=null,E=-1,L=5,_=-1;function U(){return!(e.unstable_now()-_<L)}function I(){if(j!==null){var P=e.unstable_now();_=P;var M=!0;try{M=j(!0,P)}finally{M?O():(C=!1,j=null)}}else C=!1}var O;if(typeof p=="function")O=function(){p(I)};else if(typeof MessageChannel<"u"){var T=new MessageChannel,H=T.port2;T.port1.onmessage=I,O=function(){H.postMessage(null)}}else O=function(){b(I,0)};function $(P){j=P,C||(C=!0,O())}function G(P,M){E=b(function(){P(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,$(k))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(P){switch(m){case 1:case 2:case 3:var M=3;break;default:M=m}var F=m;m=M;try{return P()}finally{m=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,M){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var F=m;m=P;try{return M()}finally{m=F}},e.unstable_scheduleCallback=function(P,M,F){var W=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?W+F:W):F=W,P){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=F+re,P={id:d++,callback:M,priorityLevel:P,startTime:F,expirationTime:re,sortIndex:-1},F>W?(P.sortIndex=F,t(c,P),n(u)===null&&P===n(c)&&(x?(g(E),E=-1):x=!0,G(N,F-W))):(P.sortIndex=re,t(u,P),v||y||(v=!0,$(k))),P},e.unstable_shouldYield=U,e.unstable_wrapCallback=function(P){var M=m;return function(){var F=m;m=M;try{return P.apply(this,arguments)}finally{m=F}}}})($f);Uf.exports=$f;var cg=Uf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dg=w,Ge=cg;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Vf=new Set,Ms={};function or(e,t){Br(e,t),Br(e+"Capture",t)}function Br(e,t){for(Ms[e]=t,e=0;e<t.length;e++)Vf.add(t[e])}var $t=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Il=Object.prototype.hasOwnProperty,fg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Pc={},Tc={};function hg(e){return Il.call(Tc,e)?!0:Il.call(Pc,e)?!1:fg.test(e)?Tc[e]=!0:(Pc[e]=!0,!1)}function pg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mg(e,t,n,r){if(t===null||typeof t>"u"||pg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Oe(e,t,n,r,s,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var we={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){we[e]=new Oe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];we[t]=new Oe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){we[e]=new Oe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){we[e]=new Oe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){we[e]=new Oe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){we[e]=new Oe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){we[e]=new Oe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){we[e]=new Oe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){we[e]=new Oe(e,5,!1,e.toLowerCase(),null,!1,!1)});var ru=/[\-:]([a-z])/g;function su(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ru,su);we[t]=new Oe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ru,su);we[t]=new Oe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ru,su);we[t]=new Oe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){we[e]=new Oe(e,1,!1,e.toLowerCase(),null,!1,!1)});we.xlinkHref=new Oe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){we[e]=new Oe(e,1,!1,e.toLowerCase(),null,!0,!0)});function ou(e,t,n,r){var s=we.hasOwnProperty(t)?we[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mg(t,n,s,r)&&(n=null),r||s===null?hg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Kt=dg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,go=Symbol.for("react.element"),ur=Symbol.for("react.portal"),cr=Symbol.for("react.fragment"),iu=Symbol.for("react.strict_mode"),Fl=Symbol.for("react.profiler"),Bf=Symbol.for("react.provider"),Hf=Symbol.for("react.context"),lu=Symbol.for("react.forward_ref"),zl=Symbol.for("react.suspense"),Ul=Symbol.for("react.suspense_list"),au=Symbol.for("react.memo"),en=Symbol.for("react.lazy"),Wf=Symbol.for("react.offscreen"),Rc=Symbol.iterator;function as(e){return e===null||typeof e!="object"?null:(e=Rc&&e[Rc]||e["@@iterator"],typeof e=="function"?e:null)}var le=Object.assign,sl;function vs(e){if(sl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);sl=t&&t[1]||""}return`
`+sl+e}var ol=!1;function il(e,t){if(!e||ol)return"";ol=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),o=r.stack.split(`
`),i=s.length-1,a=o.length-1;1<=i&&0<=a&&s[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(s[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||s[i]!==o[a]){var u=`
`+s[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=i&&0<=a);break}}}finally{ol=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vs(e):""}function gg(e){switch(e.tag){case 5:return vs(e.type);case 16:return vs("Lazy");case 13:return vs("Suspense");case 19:return vs("SuspenseList");case 0:case 2:case 15:return e=il(e.type,!1),e;case 11:return e=il(e.type.render,!1),e;case 1:return e=il(e.type,!0),e;default:return""}}function $l(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case cr:return"Fragment";case ur:return"Portal";case Fl:return"Profiler";case iu:return"StrictMode";case zl:return"Suspense";case Ul:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Hf:return(e.displayName||"Context")+".Consumer";case Bf:return(e._context.displayName||"Context")+".Provider";case lu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case au:return t=e.displayName||null,t!==null?t:$l(e.type)||"Memo";case en:t=e._payload,e=e._init;try{return $l(e(t))}catch{}}return null}function yg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $l(t);case 8:return t===iu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function jn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Qf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vg(e){var t=Qf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function yo(e){e._valueTracker||(e._valueTracker=vg(e))}function Gf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Qf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Jo(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Vl(e,t){var n=t.checked;return le({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Oc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=jn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Kf(e,t){t=t.checked,t!=null&&ou(e,"checked",t,!1)}function Bl(e,t){Kf(e,t);var n=jn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Hl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Hl(e,t.type,jn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Lc(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Hl(e,t,n){(t!=="number"||Jo(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var xs=Array.isArray;function br(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+jn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Wl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return le({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function _c(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(xs(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:jn(n)}}function qf(e,t){var n=jn(t.value),r=jn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Mc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Yf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ql(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Yf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var vo,Xf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(vo=vo||document.createElement("div"),vo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=vo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function As(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ns={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xg=["Webkit","ms","Moz","O"];Object.keys(Ns).forEach(function(e){xg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ns[t]=Ns[e]})});function Zf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ns.hasOwnProperty(e)&&Ns[e]?(""+t).trim():t+"px"}function Jf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Zf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var wg=le({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Gl(e,t){if(t){if(wg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Kl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ql=null;function uu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Yl=null,Sr=null,Nr=null;function Ac(e){if(e=lo(e)){if(typeof Yl!="function")throw Error(R(280));var t=e.stateNode;t&&(t=Ai(t),Yl(e.stateNode,e.type,t))}}function eh(e){Sr?Nr?Nr.push(e):Nr=[e]:Sr=e}function th(){if(Sr){var e=Sr,t=Nr;if(Nr=Sr=null,Ac(e),t)for(e=0;e<t.length;e++)Ac(t[e])}}function nh(e,t){return e(t)}function rh(){}var ll=!1;function sh(e,t,n){if(ll)return e(t,n);ll=!0;try{return nh(e,t,n)}finally{ll=!1,(Sr!==null||Nr!==null)&&(rh(),th())}}function Ds(e,t){var n=e.stateNode;if(n===null)return null;var r=Ai(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var Xl=!1;if($t)try{var us={};Object.defineProperty(us,"passive",{get:function(){Xl=!0}}),window.addEventListener("test",us,us),window.removeEventListener("test",us,us)}catch{Xl=!1}function bg(e,t,n,r,s,o,i,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var ks=!1,ei=null,ti=!1,Zl=null,Sg={onError:function(e){ks=!0,ei=e}};function Ng(e,t,n,r,s,o,i,a,u){ks=!1,ei=null,bg.apply(Sg,arguments)}function kg(e,t,n,r,s,o,i,a,u){if(Ng.apply(this,arguments),ks){if(ks){var c=ei;ks=!1,ei=null}else throw Error(R(198));ti||(ti=!0,Zl=c)}}function ir(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function oh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Dc(e){if(ir(e)!==e)throw Error(R(188))}function jg(e){var t=e.alternate;if(!t){if(t=ir(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return Dc(s),e;if(o===r)return Dc(s),t;o=o.sibling}throw Error(R(188))}if(n.return!==r.return)n=s,r=o;else{for(var i=!1,a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function ih(e){return e=jg(e),e!==null?lh(e):null}function lh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=lh(e);if(t!==null)return t;e=e.sibling}return null}var ah=Ge.unstable_scheduleCallback,Ic=Ge.unstable_cancelCallback,Cg=Ge.unstable_shouldYield,Eg=Ge.unstable_requestPaint,ce=Ge.unstable_now,Pg=Ge.unstable_getCurrentPriorityLevel,cu=Ge.unstable_ImmediatePriority,uh=Ge.unstable_UserBlockingPriority,ni=Ge.unstable_NormalPriority,Tg=Ge.unstable_LowPriority,ch=Ge.unstable_IdlePriority,Oi=null,jt=null;function Rg(e){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(Oi,e,void 0,(e.current.flags&128)===128)}catch{}}var ft=Math.clz32?Math.clz32:_g,Og=Math.log,Lg=Math.LN2;function _g(e){return e>>>=0,e===0?32:31-(Og(e)/Lg|0)|0}var xo=64,wo=4194304;function ws(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ri(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~s;a!==0?r=ws(a):(o&=i,o!==0&&(r=ws(o)))}else i=n&~s,i!==0?r=ws(i):o!==0&&(r=ws(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,o=t&-t,s>=o||s===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ft(t),s=1<<n,r|=e[n],t&=~s;return r}function Mg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ag(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-ft(o),a=1<<i,u=s[i];u===-1?(!(a&n)||a&r)&&(s[i]=Mg(a,t)):u<=t&&(e.expiredLanes|=a),o&=~a}}function Jl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function dh(){var e=xo;return xo<<=1,!(xo&4194240)&&(xo=64),e}function al(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function oo(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ft(t),e[t]=n}function Dg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-ft(n),o=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~o}}function du(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ft(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var Z=0;function fh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var hh,fu,ph,mh,gh,ea=!1,bo=[],gn=null,yn=null,vn=null,Is=new Map,Fs=new Map,nn=[],Ig="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fc(e,t){switch(e){case"focusin":case"focusout":gn=null;break;case"dragenter":case"dragleave":yn=null;break;case"mouseover":case"mouseout":vn=null;break;case"pointerover":case"pointerout":Is.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fs.delete(t.pointerId)}}function cs(e,t,n,r,s,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[s]},t!==null&&(t=lo(t),t!==null&&fu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Fg(e,t,n,r,s){switch(t){case"focusin":return gn=cs(gn,e,t,n,r,s),!0;case"dragenter":return yn=cs(yn,e,t,n,r,s),!0;case"mouseover":return vn=cs(vn,e,t,n,r,s),!0;case"pointerover":var o=s.pointerId;return Is.set(o,cs(Is.get(o)||null,e,t,n,r,s)),!0;case"gotpointercapture":return o=s.pointerId,Fs.set(o,cs(Fs.get(o)||null,e,t,n,r,s)),!0}return!1}function yh(e){var t=Fn(e.target);if(t!==null){var n=ir(t);if(n!==null){if(t=n.tag,t===13){if(t=oh(n),t!==null){e.blockedOn=t,gh(e.priority,function(){ph(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ta(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ql=r,n.target.dispatchEvent(r),ql=null}else return t=lo(n),t!==null&&fu(t),e.blockedOn=n,!1;t.shift()}return!0}function zc(e,t,n){Fo(e)&&n.delete(t)}function zg(){ea=!1,gn!==null&&Fo(gn)&&(gn=null),yn!==null&&Fo(yn)&&(yn=null),vn!==null&&Fo(vn)&&(vn=null),Is.forEach(zc),Fs.forEach(zc)}function ds(e,t){e.blockedOn===t&&(e.blockedOn=null,ea||(ea=!0,Ge.unstable_scheduleCallback(Ge.unstable_NormalPriority,zg)))}function zs(e){function t(s){return ds(s,e)}if(0<bo.length){ds(bo[0],e);for(var n=1;n<bo.length;n++){var r=bo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(gn!==null&&ds(gn,e),yn!==null&&ds(yn,e),vn!==null&&ds(vn,e),Is.forEach(t),Fs.forEach(t),n=0;n<nn.length;n++)r=nn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<nn.length&&(n=nn[0],n.blockedOn===null);)yh(n),n.blockedOn===null&&nn.shift()}var kr=Kt.ReactCurrentBatchConfig,si=!0;function Ug(e,t,n,r){var s=Z,o=kr.transition;kr.transition=null;try{Z=1,hu(e,t,n,r)}finally{Z=s,kr.transition=o}}function $g(e,t,n,r){var s=Z,o=kr.transition;kr.transition=null;try{Z=4,hu(e,t,n,r)}finally{Z=s,kr.transition=o}}function hu(e,t,n,r){if(si){var s=ta(e,t,n,r);if(s===null)vl(e,t,r,oi,n),Fc(e,r);else if(Fg(s,e,t,n,r))r.stopPropagation();else if(Fc(e,r),t&4&&-1<Ig.indexOf(e)){for(;s!==null;){var o=lo(s);if(o!==null&&hh(o),o=ta(e,t,n,r),o===null&&vl(e,t,r,oi,n),o===s)break;s=o}s!==null&&r.stopPropagation()}else vl(e,t,r,null,n)}}var oi=null;function ta(e,t,n,r){if(oi=null,e=uu(r),e=Fn(e),e!==null)if(t=ir(e),t===null)e=null;else if(n=t.tag,n===13){if(e=oh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return oi=e,null}function vh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Pg()){case cu:return 1;case uh:return 4;case ni:case Tg:return 16;case ch:return 536870912;default:return 16}default:return 16}}var pn=null,pu=null,zo=null;function xh(){if(zo)return zo;var e,t=pu,n=t.length,r,s="value"in pn?pn.value:pn.textContent,o=s.length;for(e=0;e<n&&t[e]===s[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===s[o-r];r++);return zo=s.slice(e,1<r?1-r:void 0)}function Uo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function So(){return!0}function Uc(){return!1}function qe(e){function t(n,r,s,o,i){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?So:Uc,this.isPropagationStopped=Uc,this}return le(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=So)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=So)},persist:function(){},isPersistent:So}),t}var es={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},mu=qe(es),io=le({},es,{view:0,detail:0}),Vg=qe(io),ul,cl,fs,Li=le({},io,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fs&&(fs&&e.type==="mousemove"?(ul=e.screenX-fs.screenX,cl=e.screenY-fs.screenY):cl=ul=0,fs=e),ul)},movementY:function(e){return"movementY"in e?e.movementY:cl}}),$c=qe(Li),Bg=le({},Li,{dataTransfer:0}),Hg=qe(Bg),Wg=le({},io,{relatedTarget:0}),dl=qe(Wg),Qg=le({},es,{animationName:0,elapsedTime:0,pseudoElement:0}),Gg=qe(Qg),Kg=le({},es,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),qg=qe(Kg),Yg=le({},es,{data:0}),Vc=qe(Yg),Xg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ey(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jg[e])?!!t[e]:!1}function gu(){return ey}var ty=le({},io,{key:function(e){if(e.key){var t=Xg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Uo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gu,charCode:function(e){return e.type==="keypress"?Uo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Uo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ny=qe(ty),ry=le({},Li,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bc=qe(ry),sy=le({},io,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gu}),oy=qe(sy),iy=le({},es,{propertyName:0,elapsedTime:0,pseudoElement:0}),ly=qe(iy),ay=le({},Li,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),uy=qe(ay),cy=[9,13,27,32],yu=$t&&"CompositionEvent"in window,js=null;$t&&"documentMode"in document&&(js=document.documentMode);var dy=$t&&"TextEvent"in window&&!js,wh=$t&&(!yu||js&&8<js&&11>=js),Hc=" ",Wc=!1;function bh(e,t){switch(e){case"keyup":return cy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var dr=!1;function fy(e,t){switch(e){case"compositionend":return Sh(t);case"keypress":return t.which!==32?null:(Wc=!0,Hc);case"textInput":return e=t.data,e===Hc&&Wc?null:e;default:return null}}function hy(e,t){if(dr)return e==="compositionend"||!yu&&bh(e,t)?(e=xh(),zo=pu=pn=null,dr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return wh&&t.locale!=="ko"?null:t.data;default:return null}}var py={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!py[e.type]:t==="textarea"}function Nh(e,t,n,r){eh(r),t=ii(t,"onChange"),0<t.length&&(n=new mu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Cs=null,Us=null;function my(e){Mh(e,0)}function _i(e){var t=pr(e);if(Gf(t))return e}function gy(e,t){if(e==="change")return t}var kh=!1;if($t){var fl;if($t){var hl="oninput"in document;if(!hl){var Gc=document.createElement("div");Gc.setAttribute("oninput","return;"),hl=typeof Gc.oninput=="function"}fl=hl}else fl=!1;kh=fl&&(!document.documentMode||9<document.documentMode)}function Kc(){Cs&&(Cs.detachEvent("onpropertychange",jh),Us=Cs=null)}function jh(e){if(e.propertyName==="value"&&_i(Us)){var t=[];Nh(t,Us,e,uu(e)),sh(my,t)}}function yy(e,t,n){e==="focusin"?(Kc(),Cs=t,Us=n,Cs.attachEvent("onpropertychange",jh)):e==="focusout"&&Kc()}function vy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _i(Us)}function xy(e,t){if(e==="click")return _i(t)}function wy(e,t){if(e==="input"||e==="change")return _i(t)}function by(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var pt=typeof Object.is=="function"?Object.is:by;function $s(e,t){if(pt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Il.call(t,s)||!pt(e[s],t[s]))return!1}return!0}function qc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Yc(e,t){var n=qc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qc(n)}}function Ch(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ch(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Eh(){for(var e=window,t=Jo();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Jo(e.document)}return t}function vu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sy(e){var t=Eh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ch(n.ownerDocument.documentElement,n)){if(r!==null&&vu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,o=Math.min(r.start,s);r=r.end===void 0?o:Math.min(r.end,s),!e.extend&&o>r&&(s=r,r=o,o=s),s=Yc(n,o);var i=Yc(n,r);s&&i&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ny=$t&&"documentMode"in document&&11>=document.documentMode,fr=null,na=null,Es=null,ra=!1;function Xc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ra||fr==null||fr!==Jo(r)||(r=fr,"selectionStart"in r&&vu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Es&&$s(Es,r)||(Es=r,r=ii(na,"onSelect"),0<r.length&&(t=new mu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=fr)))}function No(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hr={animationend:No("Animation","AnimationEnd"),animationiteration:No("Animation","AnimationIteration"),animationstart:No("Animation","AnimationStart"),transitionend:No("Transition","TransitionEnd")},pl={},Ph={};$t&&(Ph=document.createElement("div").style,"AnimationEvent"in window||(delete hr.animationend.animation,delete hr.animationiteration.animation,delete hr.animationstart.animation),"TransitionEvent"in window||delete hr.transitionend.transition);function Mi(e){if(pl[e])return pl[e];if(!hr[e])return e;var t=hr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ph)return pl[e]=t[n];return e}var Th=Mi("animationend"),Rh=Mi("animationiteration"),Oh=Mi("animationstart"),Lh=Mi("transitionend"),_h=new Map,Zc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rn(e,t){_h.set(e,t),or(t,[e])}for(var ml=0;ml<Zc.length;ml++){var gl=Zc[ml],ky=gl.toLowerCase(),jy=gl[0].toUpperCase()+gl.slice(1);Rn(ky,"on"+jy)}Rn(Th,"onAnimationEnd");Rn(Rh,"onAnimationIteration");Rn(Oh,"onAnimationStart");Rn("dblclick","onDoubleClick");Rn("focusin","onFocus");Rn("focusout","onBlur");Rn(Lh,"onTransitionEnd");Br("onMouseEnter",["mouseout","mouseover"]);Br("onMouseLeave",["mouseout","mouseover"]);Br("onPointerEnter",["pointerout","pointerover"]);Br("onPointerLeave",["pointerout","pointerover"]);or("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));or("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));or("onBeforeInput",["compositionend","keypress","textInput","paste"]);or("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));or("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));or("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var bs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Cy=new Set("cancel close invalid load scroll toggle".split(" ").concat(bs));function Jc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,kg(r,t,void 0,e),e.currentTarget=null}function Mh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==o&&s.isPropagationStopped())break e;Jc(s,a,c),o=u}else for(i=0;i<r.length;i++){if(a=r[i],u=a.instance,c=a.currentTarget,a=a.listener,u!==o&&s.isPropagationStopped())break e;Jc(s,a,c),o=u}}}if(ti)throw e=Zl,ti=!1,Zl=null,e}function te(e,t){var n=t[aa];n===void 0&&(n=t[aa]=new Set);var r=e+"__bubble";n.has(r)||(Ah(t,e,2,!1),n.add(r))}function yl(e,t,n){var r=0;t&&(r|=4),Ah(n,e,r,t)}var ko="_reactListening"+Math.random().toString(36).slice(2);function Vs(e){if(!e[ko]){e[ko]=!0,Vf.forEach(function(n){n!=="selectionchange"&&(Cy.has(n)||yl(n,!1,e),yl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ko]||(t[ko]=!0,yl("selectionchange",!1,t))}}function Ah(e,t,n,r){switch(vh(t)){case 1:var s=Ug;break;case 4:s=$g;break;default:s=hu}n=s.bind(null,t,n,e),s=void 0,!Xl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function vl(e,t,n,r,s){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(i===4)for(i=r.return;i!==null;){var u=i.tag;if((u===3||u===4)&&(u=i.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;i=i.return}for(;a!==null;){if(i=Fn(a),i===null)return;if(u=i.tag,u===5||u===6){r=o=i;continue e}a=a.parentNode}}r=r.return}sh(function(){var c=o,d=uu(n),f=[];e:{var m=_h.get(e);if(m!==void 0){var y=mu,v=e;switch(e){case"keypress":if(Uo(n)===0)break e;case"keydown":case"keyup":y=ny;break;case"focusin":v="focus",y=dl;break;case"focusout":v="blur",y=dl;break;case"beforeblur":case"afterblur":y=dl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=$c;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Hg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=oy;break;case Th:case Rh:case Oh:y=Gg;break;case Lh:y=ly;break;case"scroll":y=Vg;break;case"wheel":y=uy;break;case"copy":case"cut":case"paste":y=qg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Bc}var x=(t&4)!==0,b=!x&&e==="scroll",g=x?m!==null?m+"Capture":null:m;x=[];for(var p=c,h;p!==null;){h=p;var N=h.stateNode;if(h.tag===5&&N!==null&&(h=N,g!==null&&(N=Ds(p,g),N!=null&&x.push(Bs(p,N,h)))),b)break;p=p.return}0<x.length&&(m=new y(m,v,null,n,d),f.push({event:m,listeners:x}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",m&&n!==ql&&(v=n.relatedTarget||n.fromElement)&&(Fn(v)||v[Vt]))break e;if((y||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=c,v=v?Fn(v):null,v!==null&&(b=ir(v),v!==b||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=c),y!==v)){if(x=$c,N="onMouseLeave",g="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=Bc,N="onPointerLeave",g="onPointerEnter",p="pointer"),b=y==null?m:pr(y),h=v==null?m:pr(v),m=new x(N,p+"leave",y,n,d),m.target=b,m.relatedTarget=h,N=null,Fn(d)===c&&(x=new x(g,p+"enter",v,n,d),x.target=h,x.relatedTarget=b,N=x),b=N,y&&v)t:{for(x=y,g=v,p=0,h=x;h;h=lr(h))p++;for(h=0,N=g;N;N=lr(N))h++;for(;0<p-h;)x=lr(x),p--;for(;0<h-p;)g=lr(g),h--;for(;p--;){if(x===g||g!==null&&x===g.alternate)break t;x=lr(x),g=lr(g)}x=null}else x=null;y!==null&&ed(f,m,y,x,!1),v!==null&&b!==null&&ed(f,b,v,x,!0)}}e:{if(m=c?pr(c):window,y=m.nodeName&&m.nodeName.toLowerCase(),y==="select"||y==="input"&&m.type==="file")var k=gy;else if(Qc(m))if(kh)k=wy;else{k=vy;var C=yy}else(y=m.nodeName)&&y.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(k=xy);if(k&&(k=k(e,c))){Nh(f,k,n,d);break e}C&&C(e,m,c),e==="focusout"&&(C=m._wrapperState)&&C.controlled&&m.type==="number"&&Hl(m,"number",m.value)}switch(C=c?pr(c):window,e){case"focusin":(Qc(C)||C.contentEditable==="true")&&(fr=C,na=c,Es=null);break;case"focusout":Es=na=fr=null;break;case"mousedown":ra=!0;break;case"contextmenu":case"mouseup":case"dragend":ra=!1,Xc(f,n,d);break;case"selectionchange":if(Ny)break;case"keydown":case"keyup":Xc(f,n,d)}var j;if(yu)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else dr?bh(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&(wh&&n.locale!=="ko"&&(dr||E!=="onCompositionStart"?E==="onCompositionEnd"&&dr&&(j=xh()):(pn=d,pu="value"in pn?pn.value:pn.textContent,dr=!0)),C=ii(c,E),0<C.length&&(E=new Vc(E,e,null,n,d),f.push({event:E,listeners:C}),j?E.data=j:(j=Sh(n),j!==null&&(E.data=j)))),(j=dy?fy(e,n):hy(e,n))&&(c=ii(c,"onBeforeInput"),0<c.length&&(d=new Vc("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:c}),d.data=j))}Mh(f,t)})}function Bs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ii(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,o=s.stateNode;s.tag===5&&o!==null&&(s=o,o=Ds(e,n),o!=null&&r.unshift(Bs(e,o,s)),o=Ds(e,t),o!=null&&r.push(Bs(e,o,s))),e=e.return}return r}function lr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ed(e,t,n,r,s){for(var o=t._reactName,i=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,s?(u=Ds(n,o),u!=null&&i.unshift(Bs(n,u,a))):s||(u=Ds(n,o),u!=null&&i.push(Bs(n,u,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Ey=/\r\n?/g,Py=/\u0000|\uFFFD/g;function td(e){return(typeof e=="string"?e:""+e).replace(Ey,`
`).replace(Py,"")}function jo(e,t,n){if(t=td(t),td(e)!==t&&n)throw Error(R(425))}function li(){}var sa=null,oa=null;function ia(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var la=typeof setTimeout=="function"?setTimeout:void 0,Ty=typeof clearTimeout=="function"?clearTimeout:void 0,nd=typeof Promise=="function"?Promise:void 0,Ry=typeof queueMicrotask=="function"?queueMicrotask:typeof nd<"u"?function(e){return nd.resolve(null).then(e).catch(Oy)}:la;function Oy(e){setTimeout(function(){throw e})}function xl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),zs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);zs(t)}function xn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function rd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ts=Math.random().toString(36).slice(2),kt="__reactFiber$"+ts,Hs="__reactProps$"+ts,Vt="__reactContainer$"+ts,aa="__reactEvents$"+ts,Ly="__reactListeners$"+ts,_y="__reactHandles$"+ts;function Fn(e){var t=e[kt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Vt]||n[kt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=rd(e);e!==null;){if(n=e[kt])return n;e=rd(e)}return t}e=n,n=e.parentNode}return null}function lo(e){return e=e[kt]||e[Vt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function pr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function Ai(e){return e[Hs]||null}var ua=[],mr=-1;function On(e){return{current:e}}function ne(e){0>mr||(e.current=ua[mr],ua[mr]=null,mr--)}function J(e,t){mr++,ua[mr]=e.current,e.current=t}var Cn={},je=On(Cn),Ie=On(!1),Yn=Cn;function Hr(e,t){var n=e.type.contextTypes;if(!n)return Cn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},o;for(o in n)s[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Fe(e){return e=e.childContextTypes,e!=null}function ai(){ne(Ie),ne(je)}function sd(e,t,n){if(je.current!==Cn)throw Error(R(168));J(je,t),J(Ie,n)}function Dh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(R(108,yg(e)||"Unknown",s));return le({},n,r)}function ui(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Cn,Yn=je.current,J(je,e),J(Ie,Ie.current),!0}function od(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=Dh(e,t,Yn),r.__reactInternalMemoizedMergedChildContext=e,ne(Ie),ne(je),J(je,e)):ne(Ie),J(Ie,n)}var At=null,Di=!1,wl=!1;function Ih(e){At===null?At=[e]:At.push(e)}function My(e){Di=!0,Ih(e)}function Ln(){if(!wl&&At!==null){wl=!0;var e=0,t=Z;try{var n=At;for(Z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}At=null,Di=!1}catch(s){throw At!==null&&(At=At.slice(e+1)),ah(cu,Ln),s}finally{Z=t,wl=!1}}return null}var gr=[],yr=0,ci=null,di=0,Je=[],et=0,Xn=null,It=1,Ft="";function An(e,t){gr[yr++]=di,gr[yr++]=ci,ci=e,di=t}function Fh(e,t,n){Je[et++]=It,Je[et++]=Ft,Je[et++]=Xn,Xn=e;var r=It;e=Ft;var s=32-ft(r)-1;r&=~(1<<s),n+=1;var o=32-ft(t)+s;if(30<o){var i=s-s%5;o=(r&(1<<i)-1).toString(32),r>>=i,s-=i,It=1<<32-ft(t)+s|n<<s|r,Ft=o+e}else It=1<<o|n<<s|r,Ft=e}function xu(e){e.return!==null&&(An(e,1),Fh(e,1,0))}function wu(e){for(;e===ci;)ci=gr[--yr],gr[yr]=null,di=gr[--yr],gr[yr]=null;for(;e===Xn;)Xn=Je[--et],Je[et]=null,Ft=Je[--et],Je[et]=null,It=Je[--et],Je[et]=null}var We=null,He=null,se=!1,ct=null;function zh(e,t){var n=tt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function id(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,We=e,He=xn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,We=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Xn!==null?{id:It,overflow:Ft}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=tt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,We=e,He=null,!0):!1;default:return!1}}function ca(e){return(e.mode&1)!==0&&(e.flags&128)===0}function da(e){if(se){var t=He;if(t){var n=t;if(!id(e,t)){if(ca(e))throw Error(R(418));t=xn(n.nextSibling);var r=We;t&&id(e,t)?zh(r,n):(e.flags=e.flags&-4097|2,se=!1,We=e)}}else{if(ca(e))throw Error(R(418));e.flags=e.flags&-4097|2,se=!1,We=e}}}function ld(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;We=e}function Co(e){if(e!==We)return!1;if(!se)return ld(e),se=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ia(e.type,e.memoizedProps)),t&&(t=He)){if(ca(e))throw Uh(),Error(R(418));for(;t;)zh(e,t),t=xn(t.nextSibling)}if(ld(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=xn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=We?xn(e.stateNode.nextSibling):null;return!0}function Uh(){for(var e=He;e;)e=xn(e.nextSibling)}function Wr(){He=We=null,se=!1}function bu(e){ct===null?ct=[e]:ct.push(e)}var Ay=Kt.ReactCurrentBatchConfig;function hs(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var s=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=s.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function Eo(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ad(e){var t=e._init;return t(e._payload)}function $h(e){function t(g,p){if(e){var h=g.deletions;h===null?(g.deletions=[p],g.flags|=16):h.push(p)}}function n(g,p){if(!e)return null;for(;p!==null;)t(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function s(g,p){return g=Nn(g,p),g.index=0,g.sibling=null,g}function o(g,p,h){return g.index=h,e?(h=g.alternate,h!==null?(h=h.index,h<p?(g.flags|=2,p):h):(g.flags|=2,p)):(g.flags|=1048576,p)}function i(g){return e&&g.alternate===null&&(g.flags|=2),g}function a(g,p,h,N){return p===null||p.tag!==6?(p=El(h,g.mode,N),p.return=g,p):(p=s(p,h),p.return=g,p)}function u(g,p,h,N){var k=h.type;return k===cr?d(g,p,h.props.children,N,h.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===en&&ad(k)===p.type)?(N=s(p,h.props),N.ref=hs(g,p,h),N.return=g,N):(N=Go(h.type,h.key,h.props,null,g.mode,N),N.ref=hs(g,p,h),N.return=g,N)}function c(g,p,h,N){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=Pl(h,g.mode,N),p.return=g,p):(p=s(p,h.children||[]),p.return=g,p)}function d(g,p,h,N,k){return p===null||p.tag!==7?(p=qn(h,g.mode,N,k),p.return=g,p):(p=s(p,h),p.return=g,p)}function f(g,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=El(""+p,g.mode,h),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case go:return h=Go(p.type,p.key,p.props,null,g.mode,h),h.ref=hs(g,null,p),h.return=g,h;case ur:return p=Pl(p,g.mode,h),p.return=g,p;case en:var N=p._init;return f(g,N(p._payload),h)}if(xs(p)||as(p))return p=qn(p,g.mode,h,null),p.return=g,p;Eo(g,p)}return null}function m(g,p,h,N){var k=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return k!==null?null:a(g,p,""+h,N);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case go:return h.key===k?u(g,p,h,N):null;case ur:return h.key===k?c(g,p,h,N):null;case en:return k=h._init,m(g,p,k(h._payload),N)}if(xs(h)||as(h))return k!==null?null:d(g,p,h,N,null);Eo(g,h)}return null}function y(g,p,h,N,k){if(typeof N=="string"&&N!==""||typeof N=="number")return g=g.get(h)||null,a(p,g,""+N,k);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case go:return g=g.get(N.key===null?h:N.key)||null,u(p,g,N,k);case ur:return g=g.get(N.key===null?h:N.key)||null,c(p,g,N,k);case en:var C=N._init;return y(g,p,h,C(N._payload),k)}if(xs(N)||as(N))return g=g.get(h)||null,d(p,g,N,k,null);Eo(p,N)}return null}function v(g,p,h,N){for(var k=null,C=null,j=p,E=p=0,L=null;j!==null&&E<h.length;E++){j.index>E?(L=j,j=null):L=j.sibling;var _=m(g,j,h[E],N);if(_===null){j===null&&(j=L);break}e&&j&&_.alternate===null&&t(g,j),p=o(_,p,E),C===null?k=_:C.sibling=_,C=_,j=L}if(E===h.length)return n(g,j),se&&An(g,E),k;if(j===null){for(;E<h.length;E++)j=f(g,h[E],N),j!==null&&(p=o(j,p,E),C===null?k=j:C.sibling=j,C=j);return se&&An(g,E),k}for(j=r(g,j);E<h.length;E++)L=y(j,g,E,h[E],N),L!==null&&(e&&L.alternate!==null&&j.delete(L.key===null?E:L.key),p=o(L,p,E),C===null?k=L:C.sibling=L,C=L);return e&&j.forEach(function(U){return t(g,U)}),se&&An(g,E),k}function x(g,p,h,N){var k=as(h);if(typeof k!="function")throw Error(R(150));if(h=k.call(h),h==null)throw Error(R(151));for(var C=k=null,j=p,E=p=0,L=null,_=h.next();j!==null&&!_.done;E++,_=h.next()){j.index>E?(L=j,j=null):L=j.sibling;var U=m(g,j,_.value,N);if(U===null){j===null&&(j=L);break}e&&j&&U.alternate===null&&t(g,j),p=o(U,p,E),C===null?k=U:C.sibling=U,C=U,j=L}if(_.done)return n(g,j),se&&An(g,E),k;if(j===null){for(;!_.done;E++,_=h.next())_=f(g,_.value,N),_!==null&&(p=o(_,p,E),C===null?k=_:C.sibling=_,C=_);return se&&An(g,E),k}for(j=r(g,j);!_.done;E++,_=h.next())_=y(j,g,E,_.value,N),_!==null&&(e&&_.alternate!==null&&j.delete(_.key===null?E:_.key),p=o(_,p,E),C===null?k=_:C.sibling=_,C=_);return e&&j.forEach(function(I){return t(g,I)}),se&&An(g,E),k}function b(g,p,h,N){if(typeof h=="object"&&h!==null&&h.type===cr&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case go:e:{for(var k=h.key,C=p;C!==null;){if(C.key===k){if(k=h.type,k===cr){if(C.tag===7){n(g,C.sibling),p=s(C,h.props.children),p.return=g,g=p;break e}}else if(C.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===en&&ad(k)===C.type){n(g,C.sibling),p=s(C,h.props),p.ref=hs(g,C,h),p.return=g,g=p;break e}n(g,C);break}else t(g,C);C=C.sibling}h.type===cr?(p=qn(h.props.children,g.mode,N,h.key),p.return=g,g=p):(N=Go(h.type,h.key,h.props,null,g.mode,N),N.ref=hs(g,p,h),N.return=g,g=N)}return i(g);case ur:e:{for(C=h.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(g,p.sibling),p=s(p,h.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else t(g,p);p=p.sibling}p=Pl(h,g.mode,N),p.return=g,g=p}return i(g);case en:return C=h._init,b(g,p,C(h._payload),N)}if(xs(h))return v(g,p,h,N);if(as(h))return x(g,p,h,N);Eo(g,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(g,p.sibling),p=s(p,h),p.return=g,g=p):(n(g,p),p=El(h,g.mode,N),p.return=g,g=p),i(g)):n(g,p)}return b}var Qr=$h(!0),Vh=$h(!1),fi=On(null),hi=null,vr=null,Su=null;function Nu(){Su=vr=hi=null}function ku(e){var t=fi.current;ne(fi),e._currentValue=t}function fa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function jr(e,t){hi=e,Su=vr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(De=!0),e.firstContext=null)}function rt(e){var t=e._currentValue;if(Su!==e)if(e={context:e,memoizedValue:t,next:null},vr===null){if(hi===null)throw Error(R(308));vr=e,hi.dependencies={lanes:0,firstContext:e}}else vr=vr.next=e;return t}var zn=null;function ju(e){zn===null?zn=[e]:zn.push(e)}function Bh(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,ju(t)):(n.next=s.next,s.next=n),t.interleaved=n,Bt(e,r)}function Bt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var tn=!1;function Cu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Hh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function wn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,q&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,Bt(e,n)}return s=r.interleaved,s===null?(t.next=t,ju(r)):(t.next=s.next,s.next=t),r.interleaved=t,Bt(e,n)}function $o(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,du(e,n)}}function ud(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?s=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?s=o=t:o=o.next=t}else s=o=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function pi(e,t,n,r){var s=e.updateQueue;tn=!1;var o=s.firstBaseUpdate,i=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,i===null?o=c:i.next=c,i=u;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==i&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=u))}if(o!==null){var f=s.baseState;i=0,d=c=u=null,a=o;do{var m=a.lane,y=a.eventTime;if((r&m)===m){d!==null&&(d=d.next={eventTime:y,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(m=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){f=v.call(y,f,m);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,m=typeof v=="function"?v.call(y,f,m):v,m==null)break e;f=le({},f,m);break e;case 2:tn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,m=s.effects,m===null?s.effects=[a]:m.push(a))}else y={eventTime:y,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=y,u=f):d=d.next=y,i|=m;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;m=a,a=m.next,m.next=null,s.lastBaseUpdate=m,s.shared.pending=null}}while(!0);if(d===null&&(u=f),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do i|=s.lane,s=s.next;while(s!==t)}else o===null&&(s.shared.lanes=0);Jn|=i,e.lanes=i,e.memoizedState=f}}function cd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(R(191,s));s.call(r)}}}var ao={},Ct=On(ao),Ws=On(ao),Qs=On(ao);function Un(e){if(e===ao)throw Error(R(174));return e}function Eu(e,t){switch(J(Qs,t),J(Ws,e),J(Ct,ao),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ql(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ql(t,e)}ne(Ct),J(Ct,t)}function Gr(){ne(Ct),ne(Ws),ne(Qs)}function Wh(e){Un(Qs.current);var t=Un(Ct.current),n=Ql(t,e.type);t!==n&&(J(Ws,e),J(Ct,n))}function Pu(e){Ws.current===e&&(ne(Ct),ne(Ws))}var oe=On(0);function mi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var bl=[];function Tu(){for(var e=0;e<bl.length;e++)bl[e]._workInProgressVersionPrimary=null;bl.length=0}var Vo=Kt.ReactCurrentDispatcher,Sl=Kt.ReactCurrentBatchConfig,Zn=0,ie=null,fe=null,ge=null,gi=!1,Ps=!1,Gs=0,Dy=0;function be(){throw Error(R(321))}function Ru(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!pt(e[n],t[n]))return!1;return!0}function Ou(e,t,n,r,s,o){if(Zn=o,ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Vo.current=e===null||e.memoizedState===null?Uy:$y,e=n(r,s),Ps){o=0;do{if(Ps=!1,Gs=0,25<=o)throw Error(R(301));o+=1,ge=fe=null,t.updateQueue=null,Vo.current=Vy,e=n(r,s)}while(Ps)}if(Vo.current=yi,t=fe!==null&&fe.next!==null,Zn=0,ge=fe=ie=null,gi=!1,t)throw Error(R(300));return e}function Lu(){var e=Gs!==0;return Gs=0,e}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ge===null?ie.memoizedState=ge=e:ge=ge.next=e,ge}function st(){if(fe===null){var e=ie.alternate;e=e!==null?e.memoizedState:null}else e=fe.next;var t=ge===null?ie.memoizedState:ge.next;if(t!==null)ge=t,fe=e;else{if(e===null)throw Error(R(310));fe=e,e={memoizedState:fe.memoizedState,baseState:fe.baseState,baseQueue:fe.baseQueue,queue:fe.queue,next:null},ge===null?ie.memoizedState=ge=e:ge=ge.next=e}return ge}function Ks(e,t){return typeof t=="function"?t(e):t}function Nl(e){var t=st(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=fe,s=r.baseQueue,o=n.pending;if(o!==null){if(s!==null){var i=s.next;s.next=o.next,o.next=i}r.baseQueue=s=o,n.pending=null}if(s!==null){o=s.next,r=r.baseState;var a=i=null,u=null,c=o;do{var d=c.lane;if((Zn&d)===d)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=f,i=r):u=u.next=f,ie.lanes|=d,Jn|=d}c=c.next}while(c!==null&&c!==o);u===null?i=r:u.next=a,pt(r,t.memoizedState)||(De=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do o=s.lane,ie.lanes|=o,Jn|=o,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function kl(e){var t=st(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,o=t.memoizedState;if(s!==null){n.pending=null;var i=s=s.next;do o=e(o,i.action),i=i.next;while(i!==s);pt(o,t.memoizedState)||(De=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Qh(){}function Gh(e,t){var n=ie,r=st(),s=t(),o=!pt(r.memoizedState,s);if(o&&(r.memoizedState=s,De=!0),r=r.queue,_u(Yh.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ge!==null&&ge.memoizedState.tag&1){if(n.flags|=2048,qs(9,qh.bind(null,n,r,s,t),void 0,null),ye===null)throw Error(R(349));Zn&30||Kh(n,t,s)}return s}function Kh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function qh(e,t,n,r){t.value=n,t.getSnapshot=r,Xh(t)&&Zh(e)}function Yh(e,t,n){return n(function(){Xh(t)&&Zh(e)})}function Xh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!pt(e,n)}catch{return!0}}function Zh(e){var t=Bt(e,1);t!==null&&ht(t,e,1,-1)}function dd(e){var t=xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ks,lastRenderedState:e},t.queue=e,e=e.dispatch=zy.bind(null,ie,e),[t.memoizedState,e]}function qs(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ie.updateQueue,t===null?(t={lastEffect:null,stores:null},ie.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Jh(){return st().memoizedState}function Bo(e,t,n,r){var s=xt();ie.flags|=e,s.memoizedState=qs(1|t,n,void 0,r===void 0?null:r)}function Ii(e,t,n,r){var s=st();r=r===void 0?null:r;var o=void 0;if(fe!==null){var i=fe.memoizedState;if(o=i.destroy,r!==null&&Ru(r,i.deps)){s.memoizedState=qs(t,n,o,r);return}}ie.flags|=e,s.memoizedState=qs(1|t,n,o,r)}function fd(e,t){return Bo(8390656,8,e,t)}function _u(e,t){return Ii(2048,8,e,t)}function ep(e,t){return Ii(4,2,e,t)}function tp(e,t){return Ii(4,4,e,t)}function np(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function rp(e,t,n){return n=n!=null?n.concat([e]):null,Ii(4,4,np.bind(null,t,e),n)}function Mu(){}function sp(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ru(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function op(e,t){var n=st();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ru(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ip(e,t,n){return Zn&21?(pt(n,t)||(n=dh(),ie.lanes|=n,Jn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,De=!0),e.memoizedState=n)}function Iy(e,t){var n=Z;Z=n!==0&&4>n?n:4,e(!0);var r=Sl.transition;Sl.transition={};try{e(!1),t()}finally{Z=n,Sl.transition=r}}function lp(){return st().memoizedState}function Fy(e,t,n){var r=Sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ap(e))up(t,n);else if(n=Bh(e,t,n,r),n!==null){var s=Te();ht(n,e,r,s),cp(n,t,r)}}function zy(e,t,n){var r=Sn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ap(e))up(t,s);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(s.hasEagerState=!0,s.eagerState=a,pt(a,i)){var u=t.interleaved;u===null?(s.next=s,ju(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=Bh(e,t,s,r),n!==null&&(s=Te(),ht(n,e,r,s),cp(n,t,r))}}function ap(e){var t=e.alternate;return e===ie||t!==null&&t===ie}function up(e,t){Ps=gi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,du(e,n)}}var yi={readContext:rt,useCallback:be,useContext:be,useEffect:be,useImperativeHandle:be,useInsertionEffect:be,useLayoutEffect:be,useMemo:be,useReducer:be,useRef:be,useState:be,useDebugValue:be,useDeferredValue:be,useTransition:be,useMutableSource:be,useSyncExternalStore:be,useId:be,unstable_isNewReconciler:!1},Uy={readContext:rt,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:fd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Bo(4194308,4,np.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Bo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Bo(4,2,e,t)},useMemo:function(e,t){var n=xt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=xt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Fy.bind(null,ie,e),[r.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:dd,useDebugValue:Mu,useDeferredValue:function(e){return xt().memoizedState=e},useTransition:function(){var e=dd(!1),t=e[0];return e=Iy.bind(null,e[1]),xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ie,s=xt();if(se){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),ye===null)throw Error(R(349));Zn&30||Kh(r,t,n)}s.memoizedState=n;var o={value:n,getSnapshot:t};return s.queue=o,fd(Yh.bind(null,r,o,e),[e]),r.flags|=2048,qs(9,qh.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=xt(),t=ye.identifierPrefix;if(se){var n=Ft,r=It;n=(r&~(1<<32-ft(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Gs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Dy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},$y={readContext:rt,useCallback:sp,useContext:rt,useEffect:_u,useImperativeHandle:rp,useInsertionEffect:ep,useLayoutEffect:tp,useMemo:op,useReducer:Nl,useRef:Jh,useState:function(){return Nl(Ks)},useDebugValue:Mu,useDeferredValue:function(e){var t=st();return ip(t,fe.memoizedState,e)},useTransition:function(){var e=Nl(Ks)[0],t=st().memoizedState;return[e,t]},useMutableSource:Qh,useSyncExternalStore:Gh,useId:lp,unstable_isNewReconciler:!1},Vy={readContext:rt,useCallback:sp,useContext:rt,useEffect:_u,useImperativeHandle:rp,useInsertionEffect:ep,useLayoutEffect:tp,useMemo:op,useReducer:kl,useRef:Jh,useState:function(){return kl(Ks)},useDebugValue:Mu,useDeferredValue:function(e){var t=st();return fe===null?t.memoizedState=e:ip(t,fe.memoizedState,e)},useTransition:function(){var e=kl(Ks)[0],t=st().memoizedState;return[e,t]},useMutableSource:Qh,useSyncExternalStore:Gh,useId:lp,unstable_isNewReconciler:!1};function lt(e,t){if(e&&e.defaultProps){t=le({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ha(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:le({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Fi={isMounted:function(e){return(e=e._reactInternals)?ir(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Te(),s=Sn(e),o=zt(r,s);o.payload=t,n!=null&&(o.callback=n),t=wn(e,o,s),t!==null&&(ht(t,e,s,r),$o(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Te(),s=Sn(e),o=zt(r,s);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=wn(e,o,s),t!==null&&(ht(t,e,s,r),$o(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Te(),r=Sn(e),s=zt(n,r);s.tag=2,t!=null&&(s.callback=t),t=wn(e,s,r),t!==null&&(ht(t,e,r,n),$o(t,e,r))}};function hd(e,t,n,r,s,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!$s(n,r)||!$s(s,o):!0}function dp(e,t,n){var r=!1,s=Cn,o=t.contextType;return typeof o=="object"&&o!==null?o=rt(o):(s=Fe(t)?Yn:je.current,r=t.contextTypes,o=(r=r!=null)?Hr(e,s):Cn),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Fi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=o),t}function pd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Fi.enqueueReplaceState(t,t.state,null)}function pa(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Cu(e);var o=t.contextType;typeof o=="object"&&o!==null?s.context=rt(o):(o=Fe(t)?Yn:je.current,s.context=Hr(e,o)),s.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(ha(e,t,o,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Fi.enqueueReplaceState(s,s.state,null),pi(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Kr(e,t){try{var n="",r=t;do n+=gg(r),r=r.return;while(r);var s=n}catch(o){s=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:s,digest:null}}function jl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ma(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var By=typeof WeakMap=="function"?WeakMap:Map;function fp(e,t,n){n=zt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){xi||(xi=!0,ja=r),ma(e,t)},n}function hp(e,t,n){n=zt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){ma(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ma(e,t),typeof r!="function"&&(bn===null?bn=new Set([this]):bn.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function md(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new By;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=rv.bind(null,e,t,n),t.then(e,e))}function gd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function yd(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=zt(-1,1),t.tag=2,wn(n,t,1))),n.lanes|=1),e)}var Hy=Kt.ReactCurrentOwner,De=!1;function Pe(e,t,n,r){t.child=e===null?Vh(t,null,n,r):Qr(t,e.child,n,r)}function vd(e,t,n,r,s){n=n.render;var o=t.ref;return jr(t,s),r=Ou(e,t,n,r,o,s),n=Lu(),e!==null&&!De?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Ht(e,t,s)):(se&&n&&xu(t),t.flags|=1,Pe(e,t,r,s),t.child)}function xd(e,t,n,r,s){if(e===null){var o=n.type;return typeof o=="function"&&!Vu(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,pp(e,t,o,r,s)):(e=Go(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&s)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:$s,n(i,r)&&e.ref===t.ref)return Ht(e,t,s)}return t.flags|=1,e=Nn(o,r),e.ref=t.ref,e.return=t,t.child=e}function pp(e,t,n,r,s){if(e!==null){var o=e.memoizedProps;if($s(o,r)&&e.ref===t.ref)if(De=!1,t.pendingProps=r=o,(e.lanes&s)!==0)e.flags&131072&&(De=!0);else return t.lanes=e.lanes,Ht(e,t,s)}return ga(e,t,n,r,s)}function mp(e,t,n){var r=t.pendingProps,s=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},J(wr,Ve),Ve|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,J(wr,Ve),Ve|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,J(wr,Ve),Ve|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,J(wr,Ve),Ve|=r;return Pe(e,t,s,n),t.child}function gp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ga(e,t,n,r,s){var o=Fe(n)?Yn:je.current;return o=Hr(t,o),jr(t,s),n=Ou(e,t,n,r,o,s),r=Lu(),e!==null&&!De?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Ht(e,t,s)):(se&&r&&xu(t),t.flags|=1,Pe(e,t,n,s),t.child)}function wd(e,t,n,r,s){if(Fe(n)){var o=!0;ui(t)}else o=!1;if(jr(t,s),t.stateNode===null)Ho(e,t),dp(t,n,r),pa(t,n,r,s),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var u=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=rt(c):(c=Fe(n)?Yn:je.current,c=Hr(t,c));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||u!==c)&&pd(t,i,r,c),tn=!1;var m=t.memoizedState;i.state=m,pi(t,r,i,s),u=t.memoizedState,a!==r||m!==u||Ie.current||tn?(typeof d=="function"&&(ha(t,n,d,r),u=t.memoizedState),(a=tn||hd(t,n,a,r,m,u,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),i.props=r,i.state=u,i.context=c,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Hh(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:lt(t.type,a),i.props=c,f=t.pendingProps,m=i.context,u=n.contextType,typeof u=="object"&&u!==null?u=rt(u):(u=Fe(n)?Yn:je.current,u=Hr(t,u));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||m!==u)&&pd(t,i,r,u),tn=!1,m=t.memoizedState,i.state=m,pi(t,r,i,s);var v=t.memoizedState;a!==f||m!==v||Ie.current||tn?(typeof y=="function"&&(ha(t,n,y,r),v=t.memoizedState),(c=tn||hd(t,n,c,r,m,v,u)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,v,u),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,v,u)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),i.props=r,i.state=v,i.context=u,r=c):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return ya(e,t,n,r,o,s)}function ya(e,t,n,r,s,o){gp(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return s&&od(t,n,!1),Ht(e,t,o);r=t.stateNode,Hy.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Qr(t,e.child,null,o),t.child=Qr(t,null,a,o)):Pe(e,t,a,o),t.memoizedState=r.state,s&&od(t,n,!0),t.child}function yp(e){var t=e.stateNode;t.pendingContext?sd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&sd(e,t.context,!1),Eu(e,t.containerInfo)}function bd(e,t,n,r,s){return Wr(),bu(s),t.flags|=256,Pe(e,t,n,r),t.child}var va={dehydrated:null,treeContext:null,retryLane:0};function xa(e){return{baseLanes:e,cachePool:null,transitions:null}}function vp(e,t,n){var r=t.pendingProps,s=oe.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),J(oe,s&1),e===null)return da(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=$i(i,r,0,null),e=qn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=xa(n),t.memoizedState=va,e):Au(t,i));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return Wy(e,t,i,r,a,s,n);if(o){o=r.fallback,i=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(i&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Nn(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?o=Nn(a,o):(o=qn(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?xa(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=va,r}return o=e.child,e=o.sibling,r=Nn(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Au(e,t){return t=$i({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Po(e,t,n,r){return r!==null&&bu(r),Qr(t,e.child,null,n),e=Au(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wy(e,t,n,r,s,o,i){if(n)return t.flags&256?(t.flags&=-257,r=jl(Error(R(422))),Po(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,s=t.mode,r=$i({mode:"visible",children:r.children},s,0,null),o=qn(o,s,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Qr(t,e.child,null,i),t.child.memoizedState=xa(i),t.memoizedState=va,o);if(!(t.mode&1))return Po(e,t,i,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(R(419)),r=jl(o,r,void 0),Po(e,t,i,r)}if(a=(i&e.childLanes)!==0,De||a){if(r=ye,r!==null){switch(i&-i){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|i)?0:s,s!==0&&s!==o.retryLane&&(o.retryLane=s,Bt(e,s),ht(r,e,s,-1))}return $u(),r=jl(Error(R(421))),Po(e,t,i,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=sv.bind(null,e),s._reactRetry=t,null):(e=o.treeContext,He=xn(s.nextSibling),We=t,se=!0,ct=null,e!==null&&(Je[et++]=It,Je[et++]=Ft,Je[et++]=Xn,It=e.id,Ft=e.overflow,Xn=t),t=Au(t,r.children),t.flags|=4096,t)}function Sd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),fa(e.return,t,n)}function Cl(e,t,n,r,s){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=s)}function xp(e,t,n){var r=t.pendingProps,s=r.revealOrder,o=r.tail;if(Pe(e,t,r.children,n),r=oe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sd(e,n,t);else if(e.tag===19)Sd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(J(oe,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&mi(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Cl(t,!1,s,n,o);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&mi(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Cl(t,!0,n,null,o);break;case"together":Cl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ho(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Nn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Nn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Qy(e,t,n){switch(t.tag){case 3:yp(t),Wr();break;case 5:Wh(t);break;case 1:Fe(t.type)&&ui(t);break;case 4:Eu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;J(fi,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(J(oe,oe.current&1),t.flags|=128,null):n&t.child.childLanes?vp(e,t,n):(J(oe,oe.current&1),e=Ht(e,t,n),e!==null?e.sibling:null);J(oe,oe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return xp(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),J(oe,oe.current),r)break;return null;case 22:case 23:return t.lanes=0,mp(e,t,n)}return Ht(e,t,n)}var wp,wa,bp,Sp;wp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};wa=function(){};bp=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Un(Ct.current);var o=null;switch(n){case"input":s=Vl(e,s),r=Vl(e,r),o=[];break;case"select":s=le({},s,{value:void 0}),r=le({},r,{value:void 0}),o=[];break;case"textarea":s=Wl(e,s),r=Wl(e,r),o=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=li)}Gl(n,r);var i;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Ms.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&a[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Ms.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&te("scroll",e),o||a===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Sp=function(e,t,n,r){n!==r&&(t.flags|=4)};function ps(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Se(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gy(e,t,n){var r=t.pendingProps;switch(wu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Se(t),null;case 1:return Fe(t.type)&&ai(),Se(t),null;case 3:return r=t.stateNode,Gr(),ne(Ie),ne(je),Tu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Co(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(Pa(ct),ct=null))),wa(e,t),Se(t),null;case 5:Pu(t);var s=Un(Qs.current);if(n=t.type,e!==null&&t.stateNode!=null)bp(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Se(t),null}if(e=Un(Ct.current),Co(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[kt]=t,r[Hs]=o,e=(t.mode&1)!==0,n){case"dialog":te("cancel",r),te("close",r);break;case"iframe":case"object":case"embed":te("load",r);break;case"video":case"audio":for(s=0;s<bs.length;s++)te(bs[s],r);break;case"source":te("error",r);break;case"img":case"image":case"link":te("error",r),te("load",r);break;case"details":te("toggle",r);break;case"input":Oc(r,o),te("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},te("invalid",r);break;case"textarea":_c(r,o),te("invalid",r)}Gl(n,o),s=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&jo(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&jo(r.textContent,a,e),s=["children",""+a]):Ms.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&te("scroll",r)}switch(n){case"input":yo(r),Lc(r,o,!0);break;case"textarea":yo(r),Mc(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=li)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Yf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[kt]=t,e[Hs]=r,wp(e,t,!1,!1),t.stateNode=e;e:{switch(i=Kl(n,r),n){case"dialog":te("cancel",e),te("close",e),s=r;break;case"iframe":case"object":case"embed":te("load",e),s=r;break;case"video":case"audio":for(s=0;s<bs.length;s++)te(bs[s],e);s=r;break;case"source":te("error",e),s=r;break;case"img":case"image":case"link":te("error",e),te("load",e),s=r;break;case"details":te("toggle",e),s=r;break;case"input":Oc(e,r),s=Vl(e,r),te("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=le({},r,{value:void 0}),te("invalid",e);break;case"textarea":_c(e,r),s=Wl(e,r),te("invalid",e);break;default:s=r}Gl(n,s),a=s;for(o in a)if(a.hasOwnProperty(o)){var u=a[o];o==="style"?Jf(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Xf(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&As(e,u):typeof u=="number"&&As(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Ms.hasOwnProperty(o)?u!=null&&o==="onScroll"&&te("scroll",e):u!=null&&ou(e,o,u,i))}switch(n){case"input":yo(e),Lc(e,r,!1);break;case"textarea":yo(e),Mc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+jn(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?br(e,!!r.multiple,o,!1):r.defaultValue!=null&&br(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=li)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Se(t),null;case 6:if(e&&t.stateNode!=null)Sp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=Un(Qs.current),Un(Ct.current),Co(t)){if(r=t.stateNode,n=t.memoizedProps,r[kt]=t,(o=r.nodeValue!==n)&&(e=We,e!==null))switch(e.tag){case 3:jo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&jo(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[kt]=t,t.stateNode=r}return Se(t),null;case 13:if(ne(oe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(se&&He!==null&&t.mode&1&&!(t.flags&128))Uh(),Wr(),t.flags|=98560,o=!1;else if(o=Co(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(R(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(R(317));o[kt]=t}else Wr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Se(t),o=!1}else ct!==null&&(Pa(ct),ct=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||oe.current&1?me===0&&(me=3):$u())),t.updateQueue!==null&&(t.flags|=4),Se(t),null);case 4:return Gr(),wa(e,t),e===null&&Vs(t.stateNode.containerInfo),Se(t),null;case 10:return ku(t.type._context),Se(t),null;case 17:return Fe(t.type)&&ai(),Se(t),null;case 19:if(ne(oe),o=t.memoizedState,o===null)return Se(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)ps(o,!1);else{if(me!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=mi(e),i!==null){for(t.flags|=128,ps(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return J(oe,oe.current&1|2),t.child}e=e.sibling}o.tail!==null&&ce()>qr&&(t.flags|=128,r=!0,ps(o,!1),t.lanes=4194304)}else{if(!r)if(e=mi(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ps(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!se)return Se(t),null}else 2*ce()-o.renderingStartTime>qr&&n!==1073741824&&(t.flags|=128,r=!0,ps(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ce(),t.sibling=null,n=oe.current,J(oe,r?n&1|2:n&1),t):(Se(t),null);case 22:case 23:return Uu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ve&1073741824&&(Se(t),t.subtreeFlags&6&&(t.flags|=8192)):Se(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Ky(e,t){switch(wu(t),t.tag){case 1:return Fe(t.type)&&ai(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gr(),ne(Ie),ne(je),Tu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Pu(t),null;case 13:if(ne(oe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));Wr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ne(oe),null;case 4:return Gr(),null;case 10:return ku(t.type._context),null;case 22:case 23:return Uu(),null;case 24:return null;default:return null}}var To=!1,ke=!1,qy=typeof WeakSet=="function"?WeakSet:Set,D=null;function xr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ue(e,t,r)}else n.current=null}function ba(e,t,n){try{n()}catch(r){ue(e,t,r)}}var Nd=!1;function Yy(e,t){if(sa=si,e=Eh(),vu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,u=-1,c=0,d=0,f=e,m=null;t:for(;;){for(var y;f!==n||s!==0&&f.nodeType!==3||(a=i+s),f!==o||r!==0&&f.nodeType!==3||(u=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(y=f.firstChild)!==null;)m=f,f=y;for(;;){if(f===e)break t;if(m===n&&++c===s&&(a=i),m===o&&++d===r&&(u=i),(y=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=y}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(oa={focusedElem:e,selectionRange:n},si=!1,D=t;D!==null;)if(t=D,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,D=e;else for(;D!==null;){t=D;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,b=v.memoizedState,g=t.stateNode,p=g.getSnapshotBeforeUpdate(t.elementType===t.type?x:lt(t.type,x),b);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(N){ue(t,t.return,N)}if(e=t.sibling,e!==null){e.return=t.return,D=e;break}D=t.return}return v=Nd,Nd=!1,v}function Ts(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var o=s.destroy;s.destroy=void 0,o!==void 0&&ba(t,n,o)}s=s.next}while(s!==r)}}function zi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Sa(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Np(e){var t=e.alternate;t!==null&&(e.alternate=null,Np(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[kt],delete t[Hs],delete t[aa],delete t[Ly],delete t[_y])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function kp(e){return e.tag===5||e.tag===3||e.tag===4}function kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||kp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Na(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=li));else if(r!==4&&(e=e.child,e!==null))for(Na(e,t,n),e=e.sibling;e!==null;)Na(e,t,n),e=e.sibling}function ka(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ka(e,t,n),e=e.sibling;e!==null;)ka(e,t,n),e=e.sibling}var ve=null,ut=!1;function qt(e,t,n){for(n=n.child;n!==null;)jp(e,t,n),n=n.sibling}function jp(e,t,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(Oi,n)}catch{}switch(n.tag){case 5:ke||xr(n,t);case 6:var r=ve,s=ut;ve=null,qt(e,t,n),ve=r,ut=s,ve!==null&&(ut?(e=ve,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ve.removeChild(n.stateNode));break;case 18:ve!==null&&(ut?(e=ve,n=n.stateNode,e.nodeType===8?xl(e.parentNode,n):e.nodeType===1&&xl(e,n),zs(e)):xl(ve,n.stateNode));break;case 4:r=ve,s=ut,ve=n.stateNode.containerInfo,ut=!0,qt(e,t,n),ve=r,ut=s;break;case 0:case 11:case 14:case 15:if(!ke&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var o=s,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&ba(n,t,i),s=s.next}while(s!==r)}qt(e,t,n);break;case 1:if(!ke&&(xr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ue(n,t,a)}qt(e,t,n);break;case 21:qt(e,t,n);break;case 22:n.mode&1?(ke=(r=ke)||n.memoizedState!==null,qt(e,t,n),ke=r):qt(e,t,n);break;default:qt(e,t,n)}}function jd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new qy),t.forEach(function(r){var s=ov.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function it(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:ve=a.stateNode,ut=!1;break e;case 3:ve=a.stateNode.containerInfo,ut=!0;break e;case 4:ve=a.stateNode.containerInfo,ut=!0;break e}a=a.return}if(ve===null)throw Error(R(160));jp(o,i,s),ve=null,ut=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){ue(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Cp(t,e),t=t.sibling}function Cp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(it(t,e),vt(e),r&4){try{Ts(3,e,e.return),zi(3,e)}catch(x){ue(e,e.return,x)}try{Ts(5,e,e.return)}catch(x){ue(e,e.return,x)}}break;case 1:it(t,e),vt(e),r&512&&n!==null&&xr(n,n.return);break;case 5:if(it(t,e),vt(e),r&512&&n!==null&&xr(n,n.return),e.flags&32){var s=e.stateNode;try{As(s,"")}catch(x){ue(e,e.return,x)}}if(r&4&&(s=e.stateNode,s!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Kf(s,o),Kl(a,i);var c=Kl(a,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];d==="style"?Jf(s,f):d==="dangerouslySetInnerHTML"?Xf(s,f):d==="children"?As(s,f):ou(s,d,f,c)}switch(a){case"input":Bl(s,o);break;case"textarea":qf(s,o);break;case"select":var m=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!o.multiple;var y=o.value;y!=null?br(s,!!o.multiple,y,!1):m!==!!o.multiple&&(o.defaultValue!=null?br(s,!!o.multiple,o.defaultValue,!0):br(s,!!o.multiple,o.multiple?[]:"",!1))}s[Hs]=o}catch(x){ue(e,e.return,x)}}break;case 6:if(it(t,e),vt(e),r&4){if(e.stateNode===null)throw Error(R(162));s=e.stateNode,o=e.memoizedProps;try{s.nodeValue=o}catch(x){ue(e,e.return,x)}}break;case 3:if(it(t,e),vt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{zs(t.containerInfo)}catch(x){ue(e,e.return,x)}break;case 4:it(t,e),vt(e);break;case 13:it(t,e),vt(e),s=e.child,s.flags&8192&&(o=s.memoizedState!==null,s.stateNode.isHidden=o,!o||s.alternate!==null&&s.alternate.memoizedState!==null||(Fu=ce())),r&4&&jd(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ke=(c=ke)||d,it(t,e),ke=c):it(t,e),vt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(D=e,d=e.child;d!==null;){for(f=D=d;D!==null;){switch(m=D,y=m.child,m.tag){case 0:case 11:case 14:case 15:Ts(4,m,m.return);break;case 1:xr(m,m.return);var v=m.stateNode;if(typeof v.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){ue(r,n,x)}}break;case 5:xr(m,m.return);break;case 22:if(m.memoizedState!==null){Ed(f);continue}}y!==null?(y.return=m,D=y):Ed(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{s=f.stateNode,c?(o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,u=f.memoizedProps.style,i=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Zf("display",i))}catch(x){ue(e,e.return,x)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(x){ue(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:it(t,e),vt(e),r&4&&jd(e);break;case 21:break;default:it(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(kp(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(As(s,""),r.flags&=-33);var o=kd(e);ka(e,o,s);break;case 3:case 4:var i=r.stateNode.containerInfo,a=kd(e);Na(e,a,i);break;default:throw Error(R(161))}}catch(u){ue(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Xy(e,t,n){D=e,Ep(e)}function Ep(e,t,n){for(var r=(e.mode&1)!==0;D!==null;){var s=D,o=s.child;if(s.tag===22&&r){var i=s.memoizedState!==null||To;if(!i){var a=s.alternate,u=a!==null&&a.memoizedState!==null||ke;a=To;var c=ke;if(To=i,(ke=u)&&!c)for(D=s;D!==null;)i=D,u=i.child,i.tag===22&&i.memoizedState!==null?Pd(s):u!==null?(u.return=i,D=u):Pd(s);for(;o!==null;)D=o,Ep(o),o=o.sibling;D=s,To=a,ke=c}Cd(e)}else s.subtreeFlags&8772&&o!==null?(o.return=s,D=o):Cd(e)}}function Cd(e){for(;D!==null;){var t=D;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ke||zi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ke)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:lt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&cd(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}cd(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&zs(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}ke||t.flags&512&&Sa(t)}catch(m){ue(t,t.return,m)}}if(t===e){D=null;break}if(n=t.sibling,n!==null){n.return=t.return,D=n;break}D=t.return}}function Ed(e){for(;D!==null;){var t=D;if(t===e){D=null;break}var n=t.sibling;if(n!==null){n.return=t.return,D=n;break}D=t.return}}function Pd(e){for(;D!==null;){var t=D;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{zi(4,t)}catch(u){ue(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){ue(t,s,u)}}var o=t.return;try{Sa(t)}catch(u){ue(t,o,u)}break;case 5:var i=t.return;try{Sa(t)}catch(u){ue(t,i,u)}}}catch(u){ue(t,t.return,u)}if(t===e){D=null;break}var a=t.sibling;if(a!==null){a.return=t.return,D=a;break}D=t.return}}var Zy=Math.ceil,vi=Kt.ReactCurrentDispatcher,Du=Kt.ReactCurrentOwner,nt=Kt.ReactCurrentBatchConfig,q=0,ye=null,de=null,xe=0,Ve=0,wr=On(0),me=0,Ys=null,Jn=0,Ui=0,Iu=0,Rs=null,Ae=null,Fu=0,qr=1/0,Mt=null,xi=!1,ja=null,bn=null,Ro=!1,mn=null,wi=0,Os=0,Ca=null,Wo=-1,Qo=0;function Te(){return q&6?ce():Wo!==-1?Wo:Wo=ce()}function Sn(e){return e.mode&1?q&2&&xe!==0?xe&-xe:Ay.transition!==null?(Qo===0&&(Qo=dh()),Qo):(e=Z,e!==0||(e=window.event,e=e===void 0?16:vh(e.type)),e):1}function ht(e,t,n,r){if(50<Os)throw Os=0,Ca=null,Error(R(185));oo(e,n,r),(!(q&2)||e!==ye)&&(e===ye&&(!(q&2)&&(Ui|=n),me===4&&rn(e,xe)),ze(e,r),n===1&&q===0&&!(t.mode&1)&&(qr=ce()+500,Di&&Ln()))}function ze(e,t){var n=e.callbackNode;Ag(e,t);var r=ri(e,e===ye?xe:0);if(r===0)n!==null&&Ic(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ic(n),t===1)e.tag===0?My(Td.bind(null,e)):Ih(Td.bind(null,e)),Ry(function(){!(q&6)&&Ln()}),n=null;else{switch(fh(r)){case 1:n=cu;break;case 4:n=uh;break;case 16:n=ni;break;case 536870912:n=ch;break;default:n=ni}n=Ap(n,Pp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Pp(e,t){if(Wo=-1,Qo=0,q&6)throw Error(R(327));var n=e.callbackNode;if(Cr()&&e.callbackNode!==n)return null;var r=ri(e,e===ye?xe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=bi(e,r);else{t=r;var s=q;q|=2;var o=Rp();(ye!==e||xe!==t)&&(Mt=null,qr=ce()+500,Kn(e,t));do try{tv();break}catch(a){Tp(e,a)}while(!0);Nu(),vi.current=o,q=s,de!==null?t=0:(ye=null,xe=0,t=me)}if(t!==0){if(t===2&&(s=Jl(e),s!==0&&(r=s,t=Ea(e,s))),t===1)throw n=Ys,Kn(e,0),rn(e,r),ze(e,ce()),n;if(t===6)rn(e,r);else{if(s=e.current.alternate,!(r&30)&&!Jy(s)&&(t=bi(e,r),t===2&&(o=Jl(e),o!==0&&(r=o,t=Ea(e,o))),t===1))throw n=Ys,Kn(e,0),rn(e,r),ze(e,ce()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Dn(e,Ae,Mt);break;case 3:if(rn(e,r),(r&130023424)===r&&(t=Fu+500-ce(),10<t)){if(ri(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Te(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=la(Dn.bind(null,e,Ae,Mt),t);break}Dn(e,Ae,Mt);break;case 4:if(rn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var i=31-ft(r);o=1<<i,i=t[i],i>s&&(s=i),r&=~o}if(r=s,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Zy(r/1960))-r,10<r){e.timeoutHandle=la(Dn.bind(null,e,Ae,Mt),r);break}Dn(e,Ae,Mt);break;case 5:Dn(e,Ae,Mt);break;default:throw Error(R(329))}}}return ze(e,ce()),e.callbackNode===n?Pp.bind(null,e):null}function Ea(e,t){var n=Rs;return e.current.memoizedState.isDehydrated&&(Kn(e,t).flags|=256),e=bi(e,t),e!==2&&(t=Ae,Ae=n,t!==null&&Pa(t)),e}function Pa(e){Ae===null?Ae=e:Ae.push.apply(Ae,e)}function Jy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],o=s.getSnapshot;s=s.value;try{if(!pt(o(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function rn(e,t){for(t&=~Iu,t&=~Ui,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ft(t),r=1<<n;e[n]=-1,t&=~r}}function Td(e){if(q&6)throw Error(R(327));Cr();var t=ri(e,0);if(!(t&1))return ze(e,ce()),null;var n=bi(e,t);if(e.tag!==0&&n===2){var r=Jl(e);r!==0&&(t=r,n=Ea(e,r))}if(n===1)throw n=Ys,Kn(e,0),rn(e,t),ze(e,ce()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Dn(e,Ae,Mt),ze(e,ce()),null}function zu(e,t){var n=q;q|=1;try{return e(t)}finally{q=n,q===0&&(qr=ce()+500,Di&&Ln())}}function er(e){mn!==null&&mn.tag===0&&!(q&6)&&Cr();var t=q;q|=1;var n=nt.transition,r=Z;try{if(nt.transition=null,Z=1,e)return e()}finally{Z=r,nt.transition=n,q=t,!(q&6)&&Ln()}}function Uu(){Ve=wr.current,ne(wr)}function Kn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ty(n)),de!==null)for(n=de.return;n!==null;){var r=n;switch(wu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ai();break;case 3:Gr(),ne(Ie),ne(je),Tu();break;case 5:Pu(r);break;case 4:Gr();break;case 13:ne(oe);break;case 19:ne(oe);break;case 10:ku(r.type._context);break;case 22:case 23:Uu()}n=n.return}if(ye=e,de=e=Nn(e.current,null),xe=Ve=t,me=0,Ys=null,Iu=Ui=Jn=0,Ae=Rs=null,zn!==null){for(t=0;t<zn.length;t++)if(n=zn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=s,r.next=i}n.pending=r}zn=null}return e}function Tp(e,t){do{var n=de;try{if(Nu(),Vo.current=yi,gi){for(var r=ie.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}gi=!1}if(Zn=0,ge=fe=ie=null,Ps=!1,Gs=0,Du.current=null,n===null||n.return===null){me=1,Ys=t,de=null;break}e:{var o=e,i=n.return,a=n,u=t;if(t=xe,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=gd(i);if(y!==null){y.flags&=-257,yd(y,i,a,o,t),y.mode&1&&md(o,c,t),t=y,u=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(u),t.updateQueue=x}else v.add(u);break e}else{if(!(t&1)){md(o,c,t),$u();break e}u=Error(R(426))}}else if(se&&a.mode&1){var b=gd(i);if(b!==null){!(b.flags&65536)&&(b.flags|=256),yd(b,i,a,o,t),bu(Kr(u,a));break e}}o=u=Kr(u,a),me!==4&&(me=2),Rs===null?Rs=[o]:Rs.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var g=fp(o,u,t);ud(o,g);break e;case 1:a=u;var p=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(bn===null||!bn.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var N=hp(o,a,t);ud(o,N);break e}}o=o.return}while(o!==null)}Lp(n)}catch(k){t=k,de===n&&n!==null&&(de=n=n.return);continue}break}while(!0)}function Rp(){var e=vi.current;return vi.current=yi,e===null?yi:e}function $u(){(me===0||me===3||me===2)&&(me=4),ye===null||!(Jn&268435455)&&!(Ui&268435455)||rn(ye,xe)}function bi(e,t){var n=q;q|=2;var r=Rp();(ye!==e||xe!==t)&&(Mt=null,Kn(e,t));do try{ev();break}catch(s){Tp(e,s)}while(!0);if(Nu(),q=n,vi.current=r,de!==null)throw Error(R(261));return ye=null,xe=0,me}function ev(){for(;de!==null;)Op(de)}function tv(){for(;de!==null&&!Cg();)Op(de)}function Op(e){var t=Mp(e.alternate,e,Ve);e.memoizedProps=e.pendingProps,t===null?Lp(e):de=t,Du.current=null}function Lp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ky(n,t),n!==null){n.flags&=32767,de=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{me=6,de=null;return}}else if(n=Gy(n,t,Ve),n!==null){de=n;return}if(t=t.sibling,t!==null){de=t;return}de=t=e}while(t!==null);me===0&&(me=5)}function Dn(e,t,n){var r=Z,s=nt.transition;try{nt.transition=null,Z=1,nv(e,t,n,r)}finally{nt.transition=s,Z=r}return null}function nv(e,t,n,r){do Cr();while(mn!==null);if(q&6)throw Error(R(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Dg(e,o),e===ye&&(de=ye=null,xe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ro||(Ro=!0,Ap(ni,function(){return Cr(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=nt.transition,nt.transition=null;var i=Z;Z=1;var a=q;q|=4,Du.current=null,Yy(e,n),Cp(n,e),Sy(oa),si=!!sa,oa=sa=null,e.current=n,Xy(n),Eg(),q=a,Z=i,nt.transition=o}else e.current=n;if(Ro&&(Ro=!1,mn=e,wi=s),o=e.pendingLanes,o===0&&(bn=null),Rg(n.stateNode),ze(e,ce()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(xi)throw xi=!1,e=ja,ja=null,e;return wi&1&&e.tag!==0&&Cr(),o=e.pendingLanes,o&1?e===Ca?Os++:(Os=0,Ca=e):Os=0,Ln(),null}function Cr(){if(mn!==null){var e=fh(wi),t=nt.transition,n=Z;try{if(nt.transition=null,Z=16>e?16:e,mn===null)var r=!1;else{if(e=mn,mn=null,wi=0,q&6)throw Error(R(331));var s=q;for(q|=4,D=e.current;D!==null;){var o=D,i=o.child;if(D.flags&16){var a=o.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(D=c;D!==null;){var d=D;switch(d.tag){case 0:case 11:case 15:Ts(8,d,o)}var f=d.child;if(f!==null)f.return=d,D=f;else for(;D!==null;){d=D;var m=d.sibling,y=d.return;if(Np(d),d===c){D=null;break}if(m!==null){m.return=y,D=m;break}D=y}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var b=x.sibling;x.sibling=null,x=b}while(x!==null)}}D=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,D=i;else e:for(;D!==null;){if(o=D,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Ts(9,o,o.return)}var g=o.sibling;if(g!==null){g.return=o.return,D=g;break e}D=o.return}}var p=e.current;for(D=p;D!==null;){i=D;var h=i.child;if(i.subtreeFlags&2064&&h!==null)h.return=i,D=h;else e:for(i=p;D!==null;){if(a=D,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:zi(9,a)}}catch(k){ue(a,a.return,k)}if(a===i){D=null;break e}var N=a.sibling;if(N!==null){N.return=a.return,D=N;break e}D=a.return}}if(q=s,Ln(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(Oi,e)}catch{}r=!0}return r}finally{Z=n,nt.transition=t}}return!1}function Rd(e,t,n){t=Kr(n,t),t=fp(e,t,1),e=wn(e,t,1),t=Te(),e!==null&&(oo(e,1,t),ze(e,t))}function ue(e,t,n){if(e.tag===3)Rd(e,e,n);else for(;t!==null;){if(t.tag===3){Rd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(bn===null||!bn.has(r))){e=Kr(n,e),e=hp(t,e,1),t=wn(t,e,1),e=Te(),t!==null&&(oo(t,1,e),ze(t,e));break}}t=t.return}}function rv(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Te(),e.pingedLanes|=e.suspendedLanes&n,ye===e&&(xe&n)===n&&(me===4||me===3&&(xe&130023424)===xe&&500>ce()-Fu?Kn(e,0):Iu|=n),ze(e,t)}function _p(e,t){t===0&&(e.mode&1?(t=wo,wo<<=1,!(wo&130023424)&&(wo=4194304)):t=1);var n=Te();e=Bt(e,t),e!==null&&(oo(e,t,n),ze(e,n))}function sv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),_p(e,n)}function ov(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),_p(e,n)}var Mp;Mp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ie.current)De=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return De=!1,Qy(e,t,n);De=!!(e.flags&131072)}else De=!1,se&&t.flags&1048576&&Fh(t,di,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ho(e,t),e=t.pendingProps;var s=Hr(t,je.current);jr(t,n),s=Ou(null,t,r,e,s,n);var o=Lu();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Fe(r)?(o=!0,ui(t)):o=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Cu(t),s.updater=Fi,t.stateNode=s,s._reactInternals=t,pa(t,r,e,n),t=ya(null,t,r,!0,o,n)):(t.tag=0,se&&o&&xu(t),Pe(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ho(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=lv(r),e=lt(r,e),s){case 0:t=ga(null,t,r,e,n);break e;case 1:t=wd(null,t,r,e,n);break e;case 11:t=vd(null,t,r,e,n);break e;case 14:t=xd(null,t,r,lt(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:lt(r,s),ga(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:lt(r,s),wd(e,t,r,s,n);case 3:e:{if(yp(t),e===null)throw Error(R(387));r=t.pendingProps,o=t.memoizedState,s=o.element,Hh(e,t),pi(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){s=Kr(Error(R(423)),t),t=bd(e,t,r,n,s);break e}else if(r!==s){s=Kr(Error(R(424)),t),t=bd(e,t,r,n,s);break e}else for(He=xn(t.stateNode.containerInfo.firstChild),We=t,se=!0,ct=null,n=Vh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Wr(),r===s){t=Ht(e,t,n);break e}Pe(e,t,r,n)}t=t.child}return t;case 5:return Wh(t),e===null&&da(t),r=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,i=s.children,ia(r,s)?i=null:o!==null&&ia(r,o)&&(t.flags|=32),gp(e,t),Pe(e,t,i,n),t.child;case 6:return e===null&&da(t),null;case 13:return vp(e,t,n);case 4:return Eu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Qr(t,null,r,n):Pe(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:lt(r,s),vd(e,t,r,s,n);case 7:return Pe(e,t,t.pendingProps,n),t.child;case 8:return Pe(e,t,t.pendingProps.children,n),t.child;case 12:return Pe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,o=t.memoizedProps,i=s.value,J(fi,r._currentValue),r._currentValue=i,o!==null)if(pt(o.value,i)){if(o.children===s.children&&!Ie.current){t=Ht(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=zt(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),fa(o.return,n,t),a.lanes|=n;break}u=u.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(R(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),fa(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Pe(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,jr(t,n),s=rt(s),r=r(s),t.flags|=1,Pe(e,t,r,n),t.child;case 14:return r=t.type,s=lt(r,t.pendingProps),s=lt(r.type,s),xd(e,t,r,s,n);case 15:return pp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:lt(r,s),Ho(e,t),t.tag=1,Fe(r)?(e=!0,ui(t)):e=!1,jr(t,n),dp(t,r,s),pa(t,r,s,n),ya(null,t,r,!0,e,n);case 19:return xp(e,t,n);case 22:return mp(e,t,n)}throw Error(R(156,t.tag))};function Ap(e,t){return ah(e,t)}function iv(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function tt(e,t,n,r){return new iv(e,t,n,r)}function Vu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function lv(e){if(typeof e=="function")return Vu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===lu)return 11;if(e===au)return 14}return 2}function Nn(e,t){var n=e.alternate;return n===null?(n=tt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Go(e,t,n,r,s,o){var i=2;if(r=e,typeof e=="function")Vu(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case cr:return qn(n.children,s,o,t);case iu:i=8,s|=8;break;case Fl:return e=tt(12,n,t,s|2),e.elementType=Fl,e.lanes=o,e;case zl:return e=tt(13,n,t,s),e.elementType=zl,e.lanes=o,e;case Ul:return e=tt(19,n,t,s),e.elementType=Ul,e.lanes=o,e;case Wf:return $i(n,s,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Bf:i=10;break e;case Hf:i=9;break e;case lu:i=11;break e;case au:i=14;break e;case en:i=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=tt(i,n,t,s),t.elementType=e,t.type=r,t.lanes=o,t}function qn(e,t,n,r){return e=tt(7,e,r,t),e.lanes=n,e}function $i(e,t,n,r){return e=tt(22,e,r,t),e.elementType=Wf,e.lanes=n,e.stateNode={isHidden:!1},e}function El(e,t,n){return e=tt(6,e,null,t),e.lanes=n,e}function Pl(e,t,n){return t=tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function av(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=al(0),this.expirationTimes=al(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=al(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,s,o,i,a,u){return e=new av(e,t,n,a,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=tt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Cu(o),e}function uv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ur,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Dp(e){if(!e)return Cn;e=e._reactInternals;e:{if(ir(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Fe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Fe(n))return Dh(e,n,t)}return t}function Ip(e,t,n,r,s,o,i,a,u){return e=Bu(n,r,!0,e,s,o,i,a,u),e.context=Dp(null),n=e.current,r=Te(),s=Sn(n),o=zt(r,s),o.callback=t??null,wn(n,o,s),e.current.lanes=s,oo(e,s,r),ze(e,r),e}function Vi(e,t,n,r){var s=t.current,o=Te(),i=Sn(s);return n=Dp(n),t.context===null?t.context=n:t.pendingContext=n,t=zt(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=wn(s,t,i),e!==null&&(ht(e,s,i,o),$o(e,s,i)),i}function Si(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Od(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Hu(e,t){Od(e,t),(e=e.alternate)&&Od(e,t)}function cv(){return null}var Fp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Wu(e){this._internalRoot=e}Bi.prototype.render=Wu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Vi(e,t,null,null)};Bi.prototype.unmount=Wu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;er(function(){Vi(null,e,null,null)}),t[Vt]=null}};function Bi(e){this._internalRoot=e}Bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=mh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<nn.length&&t!==0&&t<nn[n].priority;n++);nn.splice(n,0,e),n===0&&yh(e)}};function Qu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Hi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ld(){}function dv(e,t,n,r,s){if(s){if(typeof r=="function"){var o=r;r=function(){var c=Si(i);o.call(c)}}var i=Ip(t,r,e,0,null,!1,!1,"",Ld);return e._reactRootContainer=i,e[Vt]=i.current,Vs(e.nodeType===8?e.parentNode:e),er(),i}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=Si(u);a.call(c)}}var u=Bu(e,0,!1,null,null,!1,!1,"",Ld);return e._reactRootContainer=u,e[Vt]=u.current,Vs(e.nodeType===8?e.parentNode:e),er(function(){Vi(t,u,n,r)}),u}function Wi(e,t,n,r,s){var o=n._reactRootContainer;if(o){var i=o;if(typeof s=="function"){var a=s;s=function(){var u=Si(i);a.call(u)}}Vi(t,i,e,s)}else i=dv(n,t,e,s,r);return Si(i)}hh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ws(t.pendingLanes);n!==0&&(du(t,n|1),ze(t,ce()),!(q&6)&&(qr=ce()+500,Ln()))}break;case 13:er(function(){var r=Bt(e,1);if(r!==null){var s=Te();ht(r,e,1,s)}}),Hu(e,1)}};fu=function(e){if(e.tag===13){var t=Bt(e,134217728);if(t!==null){var n=Te();ht(t,e,134217728,n)}Hu(e,134217728)}};ph=function(e){if(e.tag===13){var t=Sn(e),n=Bt(e,t);if(n!==null){var r=Te();ht(n,e,t,r)}Hu(e,t)}};mh=function(){return Z};gh=function(e,t){var n=Z;try{return Z=e,t()}finally{Z=n}};Yl=function(e,t,n){switch(t){case"input":if(Bl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Ai(r);if(!s)throw Error(R(90));Gf(r),Bl(r,s)}}}break;case"textarea":qf(e,n);break;case"select":t=n.value,t!=null&&br(e,!!n.multiple,t,!1)}};nh=zu;rh=er;var fv={usingClientEntryPoint:!1,Events:[lo,pr,Ai,eh,th,zu]},ms={findFiberByHostInstance:Fn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},hv={bundleType:ms.bundleType,version:ms.version,rendererPackageName:ms.rendererPackageName,rendererConfig:ms.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ih(e),e===null?null:e.stateNode},findFiberByHostInstance:ms.findFiberByHostInstance||cv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Oo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Oo.isDisabled&&Oo.supportsFiber)try{Oi=Oo.inject(hv),jt=Oo}catch{}}Ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fv;Ke.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Qu(t))throw Error(R(200));return uv(e,t,null,n)};Ke.createRoot=function(e,t){if(!Qu(e))throw Error(R(299));var n=!1,r="",s=Fp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Bu(e,1,!1,null,null,n,!1,r,s),e[Vt]=t.current,Vs(e.nodeType===8?e.parentNode:e),new Wu(t)};Ke.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=ih(t),e=e===null?null:e.stateNode,e};Ke.flushSync=function(e){return er(e)};Ke.hydrate=function(e,t,n){if(!Hi(t))throw Error(R(200));return Wi(null,e,t,!0,n)};Ke.hydrateRoot=function(e,t,n){if(!Qu(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,s=!1,o="",i=Fp;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Ip(t,null,e,1,n??null,s,!1,o,i),e[Vt]=t.current,Vs(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Bi(t)};Ke.render=function(e,t,n){if(!Hi(t))throw Error(R(200));return Wi(null,e,t,!1,n)};Ke.unmountComponentAtNode=function(e){if(!Hi(e))throw Error(R(40));return e._reactRootContainer?(er(function(){Wi(null,null,e,!1,function(){e._reactRootContainer=null,e[Vt]=null})}),!0):!1};Ke.unstable_batchedUpdates=zu;Ke.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Hi(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Wi(e,t,n,!1,r)};Ke.version="18.3.1-next-f1338f8080-20240426";function zp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(zp)}catch(e){console.error(e)}}zp(),zf.exports=Ke;var uo=zf.exports;const pv=Ef(uo);var Up,_d=uo;Up=_d.createRoot,_d.hydrateRoot;function mv(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,r,s,o,i=[],a="",u=e.split("/");for(u[0]||u.shift();s=u.shift();)n=s[0],n==="*"?(i.push(n),a+=s[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(r=s.indexOf("?",1),o=s.indexOf(".",1),i.push(s.substring(1,~r?r:~o?o:s.length)),a+=~r&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~r?"?":"")+"\\"+s.substring(o))):a+="/"+s;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var $p={exports:{}},Vp={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yr=w;function gv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yv=typeof Object.is=="function"?Object.is:gv,vv=Yr.useState,xv=Yr.useEffect,wv=Yr.useLayoutEffect,bv=Yr.useDebugValue;function Sv(e,t){var n=t(),r=vv({inst:{value:n,getSnapshot:t}}),s=r[0].inst,o=r[1];return wv(function(){s.value=n,s.getSnapshot=t,Tl(s)&&o({inst:s})},[e,n,t]),xv(function(){return Tl(s)&&o({inst:s}),e(function(){Tl(s)&&o({inst:s})})},[e]),bv(n),n}function Tl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yv(e,n)}catch{return!0}}function Nv(e,t){return t()}var kv=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Nv:Sv;Vp.useSyncExternalStore=Yr.useSyncExternalStore!==void 0?Yr.useSyncExternalStore:kv;$p.exports=Vp;var jv=$p.exports;const Cv=rg.useInsertionEffect,Ev=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Pv=Ev?w.useLayoutEffect:w.useEffect,Tv=Cv||Pv,Bp=e=>{const t=w.useRef([e,(...n)=>t[0](...n)]).current;return Tv(()=>{t[0]=e}),t[1]},Rv="popstate",Gu="pushState",Ku="replaceState",Ov="hashchange",Md=[Rv,Gu,Ku,Ov],Lv=e=>{for(const t of Md)addEventListener(t,e);return()=>{for(const t of Md)removeEventListener(t,e)}},Hp=(e,t)=>jv.useSyncExternalStore(Lv,e,t),_v=()=>location.search,Mv=({ssrSearch:e=""}={})=>Hp(_v,()=>e),Ad=()=>location.pathname,Av=({ssrPath:e}={})=>Hp(Ad,e?()=>e:Ad),Dv=(e,{replace:t=!1,state:n=null}={})=>history[t?Ku:Gu](n,"",e),Iv=(e={})=>[Av(e),Dv],Dd=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Dd]>"u"){for(const e of[Gu,Ku]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),r=new Event(e);return r.arguments=arguments,dispatchEvent(r),n}}Object.defineProperty(window,Dd,{value:!0})}const Fv=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",Wp=(e="")=>e==="/"?"":e,zv=(e,t)=>e[0]==="~"?e.slice(1):Wp(t)+e,Uv=(e="",t)=>Fv(Id(Wp(e)),Id(t)),Id=e=>{try{return decodeURI(e)}catch{return e}},Qp={hook:Iv,searchHook:Mv,parser:mv,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},Gp=w.createContext(Qp),co=()=>w.useContext(Gp),Kp={},qp=w.createContext(Kp),$v=()=>w.useContext(qp),Qi=e=>{const[t,n]=e.hook(e);return[Uv(e.base,t),Bp((r,s)=>n(zv(r,e.base),s))]},qu=()=>Qi(co()),Yp=(e,t,n,r)=>{const{pattern:s,keys:o}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",r),i=s.exec(n)||[],[a,...u]=i;return a!==void 0?[!0,(()=>{const c=o!==!1?Object.fromEntries(o.map((f,m)=>[f,u[m]])):i.groups;let d={...u};return c&&Object.assign(d,c),d})(),...r?[a]:[]]:[!1,null]},Vv=({children:e,...t})=>{var d,f;const n=co(),r=t.hook?Qp:n;let s=r;const[o,i]=((d=t.ssrPath)==null?void 0:d.split("?"))??[];i&&(t.ssrSearch=i,t.ssrPath=o),t.hrefs=t.hrefs??((f=t.hook)==null?void 0:f.hrefs);let a=w.useRef({}),u=a.current,c=u;for(let m in r){const y=m==="base"?r[m]+(t[m]||""):t[m]||r[m];u===c&&y!==c[m]&&(a.current=c={...c}),c[m]=y,y!==r[m]&&(s=c)}return w.createElement(Gp.Provider,{value:s,children:e})},Fd=({children:e,component:t},n)=>t?w.createElement(t,{params:n}):typeof e=="function"?e(n):e,Bv=e=>{let t=w.useRef(Kp),n=t.current;for(const r in e)e[r]!==n[r]&&(n=e);return Object.keys(e).length===0&&(n=e),t.current=n},Mn=({path:e,nest:t,match:n,...r})=>{const s=co(),[o]=Qi(s),[i,a,u]=n??Yp(s.parser,e,o,t),c=Bv({...$v(),...a});if(!i)return null;const d=u?w.createElement(Vv,{base:u},Fd(r,c)):Fd(r,c);return w.createElement(qp.Provider,{value:c,children:d})},Hv=w.forwardRef((e,t)=>{const n=co(),[r,s]=Qi(n),{to:o="",href:i=o,onClick:a,asChild:u,children:c,className:d,replace:f,state:m,...y}=e,v=Bp(b=>{b.ctrlKey||b.metaKey||b.altKey||b.shiftKey||b.button!==0||(a==null||a(b),b.defaultPrevented||(b.preventDefault(),s(i,e)))}),x=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return u&&w.isValidElement(c)?w.cloneElement(c,{onClick:v,href:x}):w.createElement("a",{...y,onClick:v,href:x,className:d!=null&&d.call?d(r===i):d,children:c,ref:t})}),Xp=e=>Array.isArray(e)?e.flatMap(t=>Xp(t&&t.type===w.Fragment?t.props.children:t)):[e],Wv=({children:e,location:t})=>{const n=co(),[r]=Qi(n);for(const s of Xp(e)){let o=0;if(w.isValidElement(s)&&(o=Yp(n.parser,s.props.path,t||r,s.props.nest))[0])return w.cloneElement(s,{match:o})}return null};var ns=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},tr=typeof window>"u"||"Deno"in globalThis;function Ze(){}function Qv(e,t){return typeof e=="function"?e(t):e}function Ta(e){return typeof e=="number"&&e>=0&&e!==1/0}function Zp(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Er(e,t){return typeof e=="function"?e(t):e}function dt(e,t){return typeof e=="function"?e(t):e}function zd(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:o,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==Yu(i,t.options))return!1}else if(!Xs(t.queryKey,i))return!1}if(n!=="all"){const u=t.isActive();if(n==="active"&&!u||n==="inactive"&&u)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||s&&s!==t.state.fetchStatus||o&&!o(t))}function Ud(e,t){const{exact:n,status:r,predicate:s,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(nr(t.options.mutationKey)!==nr(o))return!1}else if(!Xs(t.options.mutationKey,o))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function Yu(e,t){return((t==null?void 0:t.queryKeyHashFn)||nr)(e)}function nr(e){return JSON.stringify(e,(t,n)=>Ra(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function Xs(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Xs(e[n],t[n])):!1}function Jp(e,t){if(e===t)return e;const n=$d(e)&&$d(t);if(n||Ra(e)&&Ra(t)){const r=n?e:Object.keys(e),s=r.length,o=n?t:Object.keys(t),i=o.length,a=n?[]:{};let u=0;for(let c=0;c<i;c++){const d=n?c:o[c];(!n&&r.includes(d)||n)&&e[d]===void 0&&t[d]===void 0?(a[d]=void 0,u++):(a[d]=Jp(e[d],t[d]),a[d]===e[d]&&e[d]!==void 0&&u++)}return s===i&&u===s?e:a}return t}function Ni(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function $d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Ra(e){if(!Vd(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Vd(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Vd(e){return Object.prototype.toString.call(e)==="[object Object]"}function Gv(e){return new Promise(t=>{setTimeout(t,e)})}function Oa(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Jp(e,t):t}function Kv(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function qv(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Xu=Symbol();function em(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Xu?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var $n,sn,Rr,yf,Yv=(yf=class extends ns{constructor(){super();z(this,$n);z(this,sn);z(this,Rr);A(this,Rr,t=>{if(!tr&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){S(this,sn)||this.setEventListener(S(this,Rr))}onUnsubscribe(){var t;this.hasListeners()||((t=S(this,sn))==null||t.call(this),A(this,sn,void 0))}setEventListener(t){var n;A(this,Rr,t),(n=S(this,sn))==null||n.call(this),A(this,sn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){S(this,$n)!==t&&(A(this,$n,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof S(this,$n)=="boolean"?S(this,$n):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},$n=new WeakMap,sn=new WeakMap,Rr=new WeakMap,yf),Zu=new Yv,Or,on,Lr,vf,Xv=(vf=class extends ns{constructor(){super();z(this,Or,!0);z(this,on);z(this,Lr);A(this,Lr,t=>{if(!tr&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){S(this,on)||this.setEventListener(S(this,Lr))}onUnsubscribe(){var t;this.hasListeners()||((t=S(this,on))==null||t.call(this),A(this,on,void 0))}setEventListener(t){var n;A(this,Lr,t),(n=S(this,on))==null||n.call(this),A(this,on,t(this.setOnline.bind(this)))}setOnline(t){S(this,Or)!==t&&(A(this,Or,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return S(this,Or)}},Or=new WeakMap,on=new WeakMap,Lr=new WeakMap,vf),ki=new Xv;function La(){let e,t;const n=new Promise((s,o)=>{e=s,t=o});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function Zv(e){return Math.min(1e3*2**e,3e4)}function tm(e){return(e??"online")==="online"?ki.isOnline():!0}var nm=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Rl(e){return e instanceof nm}function rm(e){let t=!1,n=0,r=!1,s;const o=La(),i=x=>{var b;r||(m(new nm(x)),(b=e.abort)==null||b.call(e))},a=()=>{t=!0},u=()=>{t=!1},c=()=>Zu.isFocused()&&(e.networkMode==="always"||ki.isOnline())&&e.canRun(),d=()=>tm(e.networkMode)&&e.canRun(),f=x=>{var b;r||(r=!0,(b=e.onSuccess)==null||b.call(e,x),s==null||s(),o.resolve(x))},m=x=>{var b;r||(r=!0,(b=e.onError)==null||b.call(e,x),s==null||s(),o.reject(x))},y=()=>new Promise(x=>{var b;s=g=>{(r||c())&&x(g)},(b=e.onPause)==null||b.call(e)}).then(()=>{var x;s=void 0,r||(x=e.onContinue)==null||x.call(e)}),v=()=>{if(r)return;let x;const b=n===0?e.initialPromise:void 0;try{x=b??e.fn()}catch(g){x=Promise.reject(g)}Promise.resolve(x).then(f).catch(g=>{var C;if(r)return;const p=e.retry??(tr?0:3),h=e.retryDelay??Zv,N=typeof h=="function"?h(n,g):h,k=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,g);if(t||!k){m(g);return}n++,(C=e.onFail)==null||C.call(e,n,g),Gv(N).then(()=>c()?void 0:y()).then(()=>{t?m(g):v()})})};return{promise:o,cancel:i,continue:()=>(s==null||s(),o),cancelRetry:a,continueRetry:u,canStart:d,start:()=>(d()?v():y().then(v),o)}}function Jv(){let e=[],t=0,n=a=>{a()},r=a=>{a()},s=a=>setTimeout(a,0);const o=a=>{t?e.push(a):s(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&s(()=>{r(()=>{a.forEach(u=>{n(u)})})})};return{batch:a=>{let u;t++;try{u=a()}finally{t--,t||i()}return u},batchCalls:a=>(...u)=>{o(()=>{a(...u)})},schedule:o,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{s=a}}}var he=Jv(),Vn,xf,sm=(xf=class{constructor(){z(this,Vn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Ta(this.gcTime)&&A(this,Vn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(tr?1/0:5*60*1e3))}clearGcTimeout(){S(this,Vn)&&(clearTimeout(S(this,Vn)),A(this,Vn,void 0))}},Vn=new WeakMap,xf),_r,Mr,Xe,Ne,eo,Bn,at,_t,wf,ex=(wf=class extends sm{constructor(t){super();z(this,at);z(this,_r);z(this,Mr);z(this,Xe);z(this,Ne);z(this,eo);z(this,Bn);A(this,Bn,!1),A(this,eo,t.defaultOptions),this.setOptions(t.options),this.observers=[],A(this,Xe,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,A(this,_r,tx(this.options)),this.state=t.state??S(this,_r),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=S(this,Ne))==null?void 0:t.promise}setOptions(t){this.options={...S(this,eo),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&S(this,Xe).remove(this)}setData(t,n){const r=Oa(this.state.data,t,this.options);return B(this,at,_t).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){B(this,at,_t).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,s;const n=(r=S(this,Ne))==null?void 0:r.promise;return(s=S(this,Ne))==null||s.cancel(t),n?n.then(Ze).catch(Ze):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(S(this,_r))}isActive(){return this.observers.some(t=>dt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Xu||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Zp(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=S(this,Ne))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=S(this,Ne))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),S(this,Xe).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(S(this,Ne)&&(S(this,Bn)?S(this,Ne).cancel({revert:!0}):S(this,Ne).cancelRetry()),this.scheduleGc()),S(this,Xe).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||B(this,at,_t).call(this,{type:"invalidate"})}fetch(t,n){var u,c,d;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(S(this,Ne))return S(this,Ne).continueRetry(),S(this,Ne).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(m=>m.options.queryFn);f&&this.setOptions(f.options)}const r=new AbortController,s=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(A(this,Bn,!0),r.signal)})},o=()=>{const f=em(this.options,n),m={queryKey:this.queryKey,meta:this.meta};return s(m),A(this,Bn,!1),this.options.persister?this.options.persister(f,m,this):f(m)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:o};s(i),(u=this.options.behavior)==null||u.onFetch(i,this),A(this,Mr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&B(this,at,_t).call(this,{type:"fetch",meta:(d=i.fetchOptions)==null?void 0:d.meta});const a=f=>{var m,y,v,x;Rl(f)&&f.silent||B(this,at,_t).call(this,{type:"error",error:f}),Rl(f)||((y=(m=S(this,Xe).config).onError)==null||y.call(m,f,this),(x=(v=S(this,Xe).config).onSettled)==null||x.call(v,this.state.data,f,this)),this.scheduleGc()};return A(this,Ne,rm({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:f=>{var m,y,v,x;if(f===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(b){a(b);return}(y=(m=S(this,Xe).config).onSuccess)==null||y.call(m,f,this),(x=(v=S(this,Xe).config).onSettled)==null||x.call(v,f,this.state.error,this),this.scheduleGc()},onError:a,onFail:(f,m)=>{B(this,at,_t).call(this,{type:"failed",failureCount:f,error:m})},onPause:()=>{B(this,at,_t).call(this,{type:"pause"})},onContinue:()=>{B(this,at,_t).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),S(this,Ne).start()}},_r=new WeakMap,Mr=new WeakMap,Xe=new WeakMap,Ne=new WeakMap,eo=new WeakMap,Bn=new WeakMap,at=new WeakSet,_t=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...om(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return Rl(s)&&s.revert&&S(this,Mr)?{...S(this,Mr),fetchStatus:"idle"}:{...r,error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),he.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),S(this,Xe).notify({query:this,type:"updated",action:t})})},wf);function om(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:tm(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function tx(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var wt,bf,nx=(bf=class extends ns{constructor(t={}){super();z(this,wt);this.config=t,A(this,wt,new Map)}build(t,n,r){const s=n.queryKey,o=n.queryHash??Yu(s,n);let i=this.get(o);return i||(i=new ex({cache:this,queryKey:s,queryHash:o,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(i)),i}add(t){S(this,wt).has(t.queryHash)||(S(this,wt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=S(this,wt).get(t.queryHash);n&&(t.destroy(),n===t&&S(this,wt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){he.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return S(this,wt).get(t)}getAll(){return[...S(this,wt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>zd(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>zd(t,r)):n}notify(t){he.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){he.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){he.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},wt=new WeakMap,bf),bt,Ce,Hn,St,Jt,Sf,rx=(Sf=class extends sm{constructor(t){super();z(this,St);z(this,bt);z(this,Ce);z(this,Hn);this.mutationId=t.mutationId,A(this,Ce,t.mutationCache),A(this,bt,[]),this.state=t.state||im(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){S(this,bt).includes(t)||(S(this,bt).push(t),this.clearGcTimeout(),S(this,Ce).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){A(this,bt,S(this,bt).filter(n=>n!==t)),this.scheduleGc(),S(this,Ce).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){S(this,bt).length||(this.state.status==="pending"?this.scheduleGc():S(this,Ce).remove(this))}continue(){var t;return((t=S(this,Hn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var s,o,i,a,u,c,d,f,m,y,v,x,b,g,p,h,N,k,C,j;A(this,Hn,rm({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(E,L)=>{B(this,St,Jt).call(this,{type:"failed",failureCount:E,error:L})},onPause:()=>{B(this,St,Jt).call(this,{type:"pause"})},onContinue:()=>{B(this,St,Jt).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>S(this,Ce).canRun(this)}));const n=this.state.status==="pending",r=!S(this,Hn).canStart();try{if(!n){B(this,St,Jt).call(this,{type:"pending",variables:t,isPaused:r}),await((o=(s=S(this,Ce).config).onMutate)==null?void 0:o.call(s,t,this));const L=await((a=(i=this.options).onMutate)==null?void 0:a.call(i,t));L!==this.state.context&&B(this,St,Jt).call(this,{type:"pending",context:L,variables:t,isPaused:r})}const E=await S(this,Hn).start();return await((c=(u=S(this,Ce).config).onSuccess)==null?void 0:c.call(u,E,t,this.state.context,this)),await((f=(d=this.options).onSuccess)==null?void 0:f.call(d,E,t,this.state.context)),await((y=(m=S(this,Ce).config).onSettled)==null?void 0:y.call(m,E,null,this.state.variables,this.state.context,this)),await((x=(v=this.options).onSettled)==null?void 0:x.call(v,E,null,t,this.state.context)),B(this,St,Jt).call(this,{type:"success",data:E}),E}catch(E){try{throw await((g=(b=S(this,Ce).config).onError)==null?void 0:g.call(b,E,t,this.state.context,this)),await((h=(p=this.options).onError)==null?void 0:h.call(p,E,t,this.state.context)),await((k=(N=S(this,Ce).config).onSettled)==null?void 0:k.call(N,void 0,E,this.state.variables,this.state.context,this)),await((j=(C=this.options).onSettled)==null?void 0:j.call(C,void 0,E,t,this.state.context)),E}finally{B(this,St,Jt).call(this,{type:"error",error:E})}}finally{S(this,Ce).runNext(this)}}},bt=new WeakMap,Ce=new WeakMap,Hn=new WeakMap,St=new WeakSet,Jt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),he.batch(()=>{S(this,bt).forEach(r=>{r.onMutationUpdate(t)}),S(this,Ce).notify({mutation:this,type:"updated",action:t})})},Sf);function im(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var $e,to,Nf,sx=(Nf=class extends ns{constructor(t={}){super();z(this,$e);z(this,to);this.config=t,A(this,$e,new Map),A(this,to,Date.now())}build(t,n,r){const s=new rx({mutationCache:this,mutationId:++po(this,to)._,options:t.defaultMutationOptions(n),state:r});return this.add(s),s}add(t){const n=Lo(t),r=S(this,$e).get(n)??[];r.push(t),S(this,$e).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Lo(t);if(S(this,$e).has(n)){const s=(r=S(this,$e).get(n))==null?void 0:r.filter(o=>o!==t);s&&(s.length===0?S(this,$e).delete(n):S(this,$e).set(n,s))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=S(this,$e).get(Lo(t)))==null?void 0:r.find(s=>s.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=S(this,$e).get(Lo(t)))==null?void 0:r.find(s=>s!==t&&s.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){he.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...S(this,$e).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Ud(n,r))}findAll(t={}){return this.getAll().filter(n=>Ud(t,n))}notify(t){he.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return he.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ze))))}},$e=new WeakMap,to=new WeakMap,Nf);function Lo(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Bd(e){return{onFetch:(t,n)=>{var d,f,m,y,v;const r=t.options,s=(m=(f=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:f.fetchMore)==null?void 0:m.direction,o=((y=t.state.data)==null?void 0:y.pages)||[],i=((v=t.state.data)==null?void 0:v.pageParams)||[];let a={pages:[],pageParams:[]},u=0;const c=async()=>{let x=!1;const b=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(t.signal.aborted?x=!0:t.signal.addEventListener("abort",()=>{x=!0}),t.signal)})},g=em(t.options,t.fetchOptions),p=async(h,N,k)=>{if(x)return Promise.reject();if(N==null&&h.pages.length)return Promise.resolve(h);const C={queryKey:t.queryKey,pageParam:N,direction:k?"backward":"forward",meta:t.options.meta};b(C);const j=await g(C),{maxPages:E}=t.options,L=k?qv:Kv;return{pages:L(h.pages,j,E),pageParams:L(h.pageParams,N,E)}};if(s&&o.length){const h=s==="backward",N=h?ox:Hd,k={pages:o,pageParams:i},C=N(r,k);a=await p(k,C,h)}else{const h=e??o.length;do{const N=u===0?i[0]??r.initialPageParam:Hd(r,a);if(u>0&&N==null)break;a=await p(a,N),u++}while(u<h)}return a};t.options.persister?t.fetchFn=()=>{var x,b;return(b=(x=t.options).persister)==null?void 0:b.call(x,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function Hd(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function ox(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ae,ln,an,Ar,Dr,un,Ir,Fr,kf,ix=(kf=class{constructor(e={}){z(this,ae);z(this,ln);z(this,an);z(this,Ar);z(this,Dr);z(this,un);z(this,Ir);z(this,Fr);A(this,ae,e.queryCache||new nx),A(this,ln,e.mutationCache||new sx),A(this,an,e.defaultOptions||{}),A(this,Ar,new Map),A(this,Dr,new Map),A(this,un,0)}mount(){po(this,un)._++,S(this,un)===1&&(A(this,Ir,Zu.subscribe(async e=>{e&&(await this.resumePausedMutations(),S(this,ae).onFocus())})),A(this,Fr,ki.subscribe(async e=>{e&&(await this.resumePausedMutations(),S(this,ae).onOnline())})))}unmount(){var e,t;po(this,un)._--,S(this,un)===0&&((e=S(this,Ir))==null||e.call(this),A(this,Ir,void 0),(t=S(this,Fr))==null||t.call(this),A(this,Fr,void 0))}isFetching(e){return S(this,ae).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return S(this,ln).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=S(this,ae).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=S(this,ae).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Er(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return S(this,ae).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),s=S(this,ae).get(r.queryHash),o=s==null?void 0:s.state.data,i=Qv(t,o);if(i!==void 0)return S(this,ae).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return he.batch(()=>S(this,ae).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=S(this,ae).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=S(this,ae);he.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=S(this,ae),r={type:"active",...e};return he.batch(()=>(n.findAll(e).forEach(s=>{s.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=he.batch(()=>S(this,ae).findAll(e).map(s=>s.cancel(n)));return Promise.all(r).then(Ze).catch(Ze)}invalidateQueries(e={},t={}){return he.batch(()=>{if(S(this,ae).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=he.batch(()=>S(this,ae).findAll(e).filter(s=>!s.isDisabled()).map(s=>{let o=s.fetch(void 0,n);return n.throwOnError||(o=o.catch(Ze)),s.state.fetchStatus==="paused"?Promise.resolve():o}));return Promise.all(r).then(Ze)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=S(this,ae).build(this,t);return n.isStaleByTime(Er(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ze).catch(Ze)}fetchInfiniteQuery(e){return e.behavior=Bd(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ze).catch(Ze)}ensureInfiniteQueryData(e){return e.behavior=Bd(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ki.isOnline()?S(this,ln).resumePausedMutations():Promise.resolve()}getQueryCache(){return S(this,ae)}getMutationCache(){return S(this,ln)}getDefaultOptions(){return S(this,an)}setDefaultOptions(e){A(this,an,e)}setQueryDefaults(e,t){S(this,Ar).set(nr(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...S(this,Ar).values()];let n={};return t.forEach(r=>{Xs(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){S(this,Dr).set(nr(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...S(this,Dr).values()];let n={};return t.forEach(r=>{Xs(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...S(this,an).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Yu(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===Xu&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...S(this,an).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){S(this,ae).clear(),S(this,ln).clear()}},ae=new WeakMap,ln=new WeakMap,an=new WeakMap,Ar=new WeakMap,Dr=new WeakMap,un=new WeakMap,Ir=new WeakMap,Fr=new WeakMap,kf),_e,K,no,Ee,Wn,zr,cn,Nt,ro,Ur,$r,Qn,Gn,dn,Vr,X,Ss,_a,Ma,Aa,Da,Ia,Fa,za,lm,jf,lx=(jf=class extends ns{constructor(t,n){super();z(this,X);z(this,_e);z(this,K);z(this,no);z(this,Ee);z(this,Wn);z(this,zr);z(this,cn);z(this,Nt);z(this,ro);z(this,Ur);z(this,$r);z(this,Qn);z(this,Gn);z(this,dn);z(this,Vr,new Set);this.options=n,A(this,_e,t),A(this,Nt,null),A(this,cn,La()),this.options.experimental_prefetchInRender||S(this,cn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(S(this,K).addObserver(this),Wd(S(this,K),this.options)?B(this,X,Ss).call(this):this.updateResult(),B(this,X,Da).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Ua(S(this,K),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Ua(S(this,K),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,B(this,X,Ia).call(this),B(this,X,Fa).call(this),S(this,K).removeObserver(this)}setOptions(t,n){const r=this.options,s=S(this,K);if(this.options=S(this,_e).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof dt(this.options.enabled,S(this,K))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");B(this,X,za).call(this),S(this,K).setOptions(this.options),r._defaulted&&!Ni(this.options,r)&&S(this,_e).getQueryCache().notify({type:"observerOptionsUpdated",query:S(this,K),observer:this});const o=this.hasListeners();o&&Qd(S(this,K),s,this.options,r)&&B(this,X,Ss).call(this),this.updateResult(n),o&&(S(this,K)!==s||dt(this.options.enabled,S(this,K))!==dt(r.enabled,S(this,K))||Er(this.options.staleTime,S(this,K))!==Er(r.staleTime,S(this,K)))&&B(this,X,_a).call(this);const i=B(this,X,Ma).call(this);o&&(S(this,K)!==s||dt(this.options.enabled,S(this,K))!==dt(r.enabled,S(this,K))||i!==S(this,dn))&&B(this,X,Aa).call(this,i)}getOptimisticResult(t){const n=S(this,_e).getQueryCache().build(S(this,_e),t),r=this.createResult(n,t);return ux(this,r)&&(A(this,Ee,r),A(this,zr,this.options),A(this,Wn,S(this,K).state)),r}getCurrentResult(){return S(this,Ee)}trackResult(t,n){const r={};return Object.keys(t).forEach(s=>{Object.defineProperty(r,s,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(s),n==null||n(s),t[s])})}),r}trackProp(t){S(this,Vr).add(t)}getCurrentQuery(){return S(this,K)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=S(this,_e).defaultQueryOptions(t),r=S(this,_e).getQueryCache().build(S(this,_e),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return B(this,X,Ss).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),S(this,Ee)))}createResult(t,n){var E;const r=S(this,K),s=this.options,o=S(this,Ee),i=S(this,Wn),a=S(this,zr),c=t!==r?t.state:S(this,no),{state:d}=t;let f={...d},m=!1,y;if(n._optimisticResults){const L=this.hasListeners(),_=!L&&Wd(t,n),U=L&&Qd(t,r,n,s);(_||U)&&(f={...f,...om(d.data,t.options)}),n._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:v,errorUpdatedAt:x,status:b}=f;if(n.select&&f.data!==void 0)if(o&&f.data===(i==null?void 0:i.data)&&n.select===S(this,ro))y=S(this,Ur);else try{A(this,ro,n.select),y=n.select(f.data),y=Oa(o==null?void 0:o.data,y,n),A(this,Ur,y),A(this,Nt,null)}catch(L){A(this,Nt,L)}else y=f.data;if(n.placeholderData!==void 0&&y===void 0&&b==="pending"){let L;if(o!=null&&o.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData))L=o.data;else if(L=typeof n.placeholderData=="function"?n.placeholderData((E=S(this,$r))==null?void 0:E.state.data,S(this,$r)):n.placeholderData,n.select&&L!==void 0)try{L=n.select(L),A(this,Nt,null)}catch(_){A(this,Nt,_)}L!==void 0&&(b="success",y=Oa(o==null?void 0:o.data,L,n),m=!0)}S(this,Nt)&&(v=S(this,Nt),y=S(this,Ur),x=Date.now(),b="error");const g=f.fetchStatus==="fetching",p=b==="pending",h=b==="error",N=p&&g,k=y!==void 0,j={status:b,fetchStatus:f.fetchStatus,isPending:p,isSuccess:b==="success",isError:h,isInitialLoading:N,isLoading:N,data:y,dataUpdatedAt:f.dataUpdatedAt,error:v,errorUpdatedAt:x,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:g,isRefetching:g&&!p,isLoadingError:h&&!k,isPaused:f.fetchStatus==="paused",isPlaceholderData:m,isRefetchError:h&&k,isStale:Ju(t,n),refetch:this.refetch,promise:S(this,cn)};if(this.options.experimental_prefetchInRender){const L=I=>{j.status==="error"?I.reject(j.error):j.data!==void 0&&I.resolve(j.data)},_=()=>{const I=A(this,cn,j.promise=La());L(I)},U=S(this,cn);switch(U.status){case"pending":t.queryHash===r.queryHash&&L(U);break;case"fulfilled":(j.status==="error"||j.data!==U.value)&&_();break;case"rejected":(j.status!=="error"||j.error!==U.reason)&&_();break}}return j}updateResult(t){const n=S(this,Ee),r=this.createResult(S(this,K),this.options);if(A(this,Wn,S(this,K).state),A(this,zr,this.options),S(this,Wn).data!==void 0&&A(this,$r,S(this,K)),Ni(r,n))return;A(this,Ee,r);const s={},o=()=>{if(!n)return!0;const{notifyOnChangeProps:i}=this.options,a=typeof i=="function"?i():i;if(a==="all"||!a&&!S(this,Vr).size)return!0;const u=new Set(a??S(this,Vr));return this.options.throwOnError&&u.add("error"),Object.keys(S(this,Ee)).some(c=>{const d=c;return S(this,Ee)[d]!==n[d]&&u.has(d)})};(t==null?void 0:t.listeners)!==!1&&o()&&(s.listeners=!0),B(this,X,lm).call(this,{...s,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&B(this,X,Da).call(this)}},_e=new WeakMap,K=new WeakMap,no=new WeakMap,Ee=new WeakMap,Wn=new WeakMap,zr=new WeakMap,cn=new WeakMap,Nt=new WeakMap,ro=new WeakMap,Ur=new WeakMap,$r=new WeakMap,Qn=new WeakMap,Gn=new WeakMap,dn=new WeakMap,Vr=new WeakMap,X=new WeakSet,Ss=function(t){B(this,X,za).call(this);let n=S(this,K).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(Ze)),n},_a=function(){B(this,X,Ia).call(this);const t=Er(this.options.staleTime,S(this,K));if(tr||S(this,Ee).isStale||!Ta(t))return;const r=Zp(S(this,Ee).dataUpdatedAt,t)+1;A(this,Qn,setTimeout(()=>{S(this,Ee).isStale||this.updateResult()},r))},Ma=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(S(this,K)):this.options.refetchInterval)??!1},Aa=function(t){B(this,X,Fa).call(this),A(this,dn,t),!(tr||dt(this.options.enabled,S(this,K))===!1||!Ta(S(this,dn))||S(this,dn)===0)&&A(this,Gn,setInterval(()=>{(this.options.refetchIntervalInBackground||Zu.isFocused())&&B(this,X,Ss).call(this)},S(this,dn)))},Da=function(){B(this,X,_a).call(this),B(this,X,Aa).call(this,B(this,X,Ma).call(this))},Ia=function(){S(this,Qn)&&(clearTimeout(S(this,Qn)),A(this,Qn,void 0))},Fa=function(){S(this,Gn)&&(clearInterval(S(this,Gn)),A(this,Gn,void 0))},za=function(){const t=S(this,_e).getQueryCache().build(S(this,_e),this.options);if(t===S(this,K))return;const n=S(this,K);A(this,K,t),A(this,no,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},lm=function(t){he.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(S(this,Ee))}),S(this,_e).getQueryCache().notify({query:S(this,K),type:"observerResultsUpdated"})})},jf);function ax(e,t){return dt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Wd(e,t){return ax(e,t)||e.state.data!==void 0&&Ua(e,t,t.refetchOnMount)}function Ua(e,t,n){if(dt(t.enabled,e)!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Ju(e,t)}return!1}function Qd(e,t,n,r){return(e!==t||dt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&Ju(e,n)}function Ju(e,t){return dt(t.enabled,e)!==!1&&e.isStaleByTime(Er(t.staleTime,e))}function ux(e,t){return!Ni(e.getCurrentResult(),t)}var fn,hn,Me,Dt,Ut,Ko,$a,Cf,cx=(Cf=class extends ns{constructor(n,r){super();z(this,Ut);z(this,fn);z(this,hn);z(this,Me);z(this,Dt);A(this,fn,n),this.setOptions(r),this.bindMethods(),B(this,Ut,Ko).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(n){var s;const r=this.options;this.options=S(this,fn).defaultMutationOptions(n),Ni(this.options,r)||S(this,fn).getMutationCache().notify({type:"observerOptionsUpdated",mutation:S(this,Me),observer:this}),r!=null&&r.mutationKey&&this.options.mutationKey&&nr(r.mutationKey)!==nr(this.options.mutationKey)?this.reset():((s=S(this,Me))==null?void 0:s.state.status)==="pending"&&S(this,Me).setOptions(this.options)}onUnsubscribe(){var n;this.hasListeners()||(n=S(this,Me))==null||n.removeObserver(this)}onMutationUpdate(n){B(this,Ut,Ko).call(this),B(this,Ut,$a).call(this,n)}getCurrentResult(){return S(this,hn)}reset(){var n;(n=S(this,Me))==null||n.removeObserver(this),A(this,Me,void 0),B(this,Ut,Ko).call(this),B(this,Ut,$a).call(this)}mutate(n,r){var s;return A(this,Dt,r),(s=S(this,Me))==null||s.removeObserver(this),A(this,Me,S(this,fn).getMutationCache().build(S(this,fn),this.options)),S(this,Me).addObserver(this),S(this,Me).execute(n)}},fn=new WeakMap,hn=new WeakMap,Me=new WeakMap,Dt=new WeakMap,Ut=new WeakSet,Ko=function(){var r;const n=((r=S(this,Me))==null?void 0:r.state)??im();A(this,hn,{...n,isPending:n.status==="pending",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset})},$a=function(n){he.batch(()=>{var r,s,o,i,a,u,c,d;if(S(this,Dt)&&this.hasListeners()){const f=S(this,hn).variables,m=S(this,hn).context;(n==null?void 0:n.type)==="success"?((s=(r=S(this,Dt)).onSuccess)==null||s.call(r,n.data,f,m),(i=(o=S(this,Dt)).onSettled)==null||i.call(o,n.data,null,f,m)):(n==null?void 0:n.type)==="error"&&((u=(a=S(this,Dt)).onError)==null||u.call(a,n.error,f,m),(d=(c=S(this,Dt)).onSettled)==null||d.call(c,void 0,n.error,f,m))}this.listeners.forEach(f=>{f(S(this,hn))})})},Cf),am=w.createContext(void 0),ec=e=>{const t=w.useContext(am);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},dx=({client:e,children:t})=>(w.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),l.jsx(am.Provider,{value:e,children:t})),um=w.createContext(!1),fx=()=>w.useContext(um);um.Provider;function hx(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var px=w.createContext(hx()),mx=()=>w.useContext(px);function cm(e,t){return typeof e=="function"?e(...t):!!e}function Va(){}var gx=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},yx=e=>{w.useEffect(()=>{e.clearReset()},[e])},vx=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&cm(n,[e.error,r]),xx=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},wx=(e,t)=>e.isLoading&&e.isFetching&&!t,bx=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Gd=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function Sx(e,t,n){var d,f,m,y,v;const r=ec(),s=fx(),o=mx(),i=r.defaultQueryOptions(e);(f=(d=r.getDefaultOptions().queries)==null?void 0:d._experimental_beforeQuery)==null||f.call(d,i),i._optimisticResults=s?"isRestoring":"optimistic",xx(i),gx(i,o),yx(o);const a=!r.getQueryCache().get(i.queryHash),[u]=w.useState(()=>new t(r,i)),c=u.getOptimisticResult(i);if(w.useSyncExternalStore(w.useCallback(x=>{const b=s?Va:u.subscribe(he.batchCalls(x));return u.updateResult(),b},[u,s]),()=>u.getCurrentResult(),()=>u.getCurrentResult()),w.useEffect(()=>{u.setOptions(i,{listeners:!1})},[i,u]),bx(i,c))throw Gd(i,u,o);if(vx({result:c,errorResetBoundary:o,throwOnError:i.throwOnError,query:r.getQueryCache().get(i.queryHash)}))throw c.error;if((y=(m=r.getDefaultOptions().queries)==null?void 0:m._experimental_afterQuery)==null||y.call(m,i,c),i.experimental_prefetchInRender&&!tr&&wx(c,s)){const x=a?Gd(i,u,o):(v=r.getQueryCache().get(i.queryHash))==null?void 0:v.promise;x==null||x.catch(Va).finally(()=>{u.updateResult()})}return i.notifyOnChangeProps?c:u.trackResult(c)}function tc(e,t){return Sx(e,lx)}function Nx(e,t){const n=ec(),[r]=w.useState(()=>new cx(n,e));w.useEffect(()=>{r.setOptions(e)},[r,e]);const s=w.useSyncExternalStore(w.useCallback(i=>r.subscribe(he.batchCalls(i)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),o=w.useCallback((i,a)=>{r.mutate(i,a).catch(Va)},[r]);if(s.error&&cm(r.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:o,mutateAsync:s.mutate}}async function dm(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}async function kx(e,t,n){const r=await fetch(t,{method:e,headers:n?{"Content-Type":"application/json"}:{},body:n?JSON.stringify(n):void 0,credentials:"include"});return await dm(r),r}const jx=({on401:e})=>async({queryKey:t})=>{const n=await fetch(t.join("/"),{credentials:"include"});return e==="returnNull"&&n.status===401?null:(await dm(n),await n.json())},Cx=new ix({defaultOptions:{queries:{queryFn:jx({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),Ex=1,Px=1e6;let Ol=0;function Tx(){return Ol=(Ol+1)%Number.MAX_SAFE_INTEGER,Ol.toString()}const Ll=new Map,Kd=e=>{if(Ll.has(e))return;const t=setTimeout(()=>{Ll.delete(e),Ls({type:"REMOVE_TOAST",toastId:e})},Px);Ll.set(e,t)},Rx=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Ex)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Kd(n):e.toasts.forEach(r=>{Kd(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},qo=[];let Yo={toasts:[]};function Ls(e){Yo=Rx(Yo,e),qo.forEach(t=>{t(Yo)})}function Ox({...e}){const t=Tx(),n=s=>Ls({type:"UPDATE_TOAST",toast:{...s,id:t}}),r=()=>Ls({type:"DISMISS_TOAST",toastId:t});return Ls({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:s=>{s||r()}}}),{id:t,dismiss:r,update:n}}function fm(){const[e,t]=w.useState(Yo);return w.useEffect(()=>(qo.push(t),()=>{const n=qo.indexOf(t);n>-1&&qo.splice(n,1)}),[e]),{...e,toast:Ox,dismiss:n=>Ls({type:"DISMISS_TOAST",toastId:n})}}function pe(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}function qd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function hm(...e){return t=>{let n=!1;const r=e.map(s=>{const o=qd(s,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let s=0;s<r.length;s++){const o=r[s];typeof o=="function"?o():qd(e[s],null)}}}}function mt(...e){return w.useCallback(hm(...e),e)}function Gi(e,t=[]){let n=[];function r(o,i){const a=w.createContext(i),u=n.length;n=[...n,i];const c=f=>{var g;const{scope:m,children:y,...v}=f,x=((g=m==null?void 0:m[e])==null?void 0:g[u])||a,b=w.useMemo(()=>v,Object.values(v));return l.jsx(x.Provider,{value:b,children:y})};c.displayName=o+"Provider";function d(f,m){var x;const y=((x=m==null?void 0:m[e])==null?void 0:x[u])||a,v=w.useContext(y);if(v)return v;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${o}\``)}return[c,d]}const s=()=>{const o=n.map(i=>w.createContext(i));return function(a){const u=(a==null?void 0:a[e])||o;return w.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return s.scopeName=e,[r,Lx(s,...t)]}function Lx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(o){const i=r.reduce((a,{useScope:u,scopeName:c})=>{const f=u(o)[`__scope${c}`];return{...a,...f}},{});return w.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function ji(e){const t=Mx(e),n=w.forwardRef((r,s)=>{const{children:o,...i}=r,a=w.Children.toArray(o),u=a.find(Dx);if(u){const c=u.props.children,d=a.map(f=>f===u?w.Children.count(c)>1?w.Children.only(null):w.isValidElement(c)?c.props.children:null:f);return l.jsx(t,{...i,ref:s,children:w.isValidElement(c)?w.cloneElement(c,void 0,d):null})}return l.jsx(t,{...i,ref:s,children:o})});return n.displayName=`${e}.Slot`,n}var _x=ji("Slot");function Mx(e){const t=w.forwardRef((n,r)=>{const{children:s,...o}=n;if(w.isValidElement(s)){const i=Fx(s),a=Ix(o,s.props);return s.type!==w.Fragment&&(a.ref=r?hm(r,i):i),w.cloneElement(s,a)}return w.Children.count(s)>1?w.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var pm=Symbol("radix.slottable");function Ax(e){const t=({children:n})=>l.jsx(l.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=pm,t}function Dx(e){return w.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===pm}function Ix(e,t){const n={...t};for(const r in t){const s=e[r],o=t[r];/^on[A-Z]/.test(r)?s&&o?n[r]=(...a)=>{o(...a),s(...a)}:s&&(n[r]=s):r==="style"?n[r]={...s,...o}:r==="className"&&(n[r]=[s,o].filter(Boolean).join(" "))}return{...e,...n}}function Fx(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function zx(e){const t=e+"CollectionProvider",[n,r]=Gi(t),[s,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=x=>{const{scope:b,children:g}=x,p=Zt.useRef(null),h=Zt.useRef(new Map).current;return l.jsx(s,{scope:b,itemMap:h,collectionRef:p,children:g})};i.displayName=t;const a=e+"CollectionSlot",u=ji(a),c=Zt.forwardRef((x,b)=>{const{scope:g,children:p}=x,h=o(a,g),N=mt(b,h.collectionRef);return l.jsx(u,{ref:N,children:p})});c.displayName=a;const d=e+"CollectionItemSlot",f="data-radix-collection-item",m=ji(d),y=Zt.forwardRef((x,b)=>{const{scope:g,children:p,...h}=x,N=Zt.useRef(null),k=mt(b,N),C=o(d,g);return Zt.useEffect(()=>(C.itemMap.set(N,{ref:N,...h}),()=>void C.itemMap.delete(N))),l.jsx(m,{[f]:"",ref:k,children:p})});y.displayName=d;function v(x){const b=o(e+"CollectionConsumer",x);return Zt.useCallback(()=>{const p=b.collectionRef.current;if(!p)return[];const h=Array.from(p.querySelectorAll(`[${f}]`));return Array.from(b.itemMap.values()).sort((C,j)=>h.indexOf(C.ref.current)-h.indexOf(j.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:i,Slot:c,ItemSlot:y},v,r]}var Ux=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ue=Ux.reduce((e,t)=>{const n=ji(`Primitive.${t}`),r=w.forwardRef((s,o)=>{const{asChild:i,...a}=s,u=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(u,{...a,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function mm(e,t){e&&uo.flushSync(()=>e.dispatchEvent(t))}function Pt(e){const t=w.useRef(e);return w.useEffect(()=>{t.current=e}),w.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function $x(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e);w.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Vx="DismissableLayer",Ba="dismissableLayer.update",Bx="dismissableLayer.pointerDownOutside",Hx="dismissableLayer.focusOutside",Yd,gm=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),nc=w.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:o,onInteractOutside:i,onDismiss:a,...u}=e,c=w.useContext(gm),[d,f]=w.useState(null),m=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=w.useState({}),v=mt(t,j=>f(j)),x=Array.from(c.layers),[b]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),g=x.indexOf(b),p=d?x.indexOf(d):-1,h=c.layersWithOutsidePointerEventsDisabled.size>0,N=p>=g,k=Qx(j=>{const E=j.target,L=[...c.branches].some(_=>_.contains(E));!N||L||(s==null||s(j),i==null||i(j),j.defaultPrevented||a==null||a())},m),C=Gx(j=>{const E=j.target;[...c.branches].some(_=>_.contains(E))||(o==null||o(j),i==null||i(j),j.defaultPrevented||a==null||a())},m);return $x(j=>{p===c.layers.size-1&&(r==null||r(j),!j.defaultPrevented&&a&&(j.preventDefault(),a()))},m),w.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Yd=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),Xd(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Yd)}},[d,m,n,c]),w.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),Xd())},[d,c]),w.useEffect(()=>{const j=()=>y({});return document.addEventListener(Ba,j),()=>document.removeEventListener(Ba,j)},[]),l.jsx(Ue.div,{...u,ref:v,style:{pointerEvents:h?N?"auto":"none":void 0,...e.style},onFocusCapture:pe(e.onFocusCapture,C.onFocusCapture),onBlurCapture:pe(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:pe(e.onPointerDownCapture,k.onPointerDownCapture)})});nc.displayName=Vx;var Wx="DismissableLayerBranch",ym=w.forwardRef((e,t)=>{const n=w.useContext(gm),r=w.useRef(null),s=mt(t,r);return w.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),l.jsx(Ue.div,{...e,ref:s})});ym.displayName=Wx;function Qx(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e),r=w.useRef(!1),s=w.useRef(()=>{});return w.useEffect(()=>{const o=a=>{if(a.target&&!r.current){let u=function(){vm(Bx,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=u,t.addEventListener("click",s.current,{once:!0})):u()}else t.removeEventListener("click",s.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",o),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Gx(e,t=globalThis==null?void 0:globalThis.document){const n=Pt(e),r=w.useRef(!1);return w.useEffect(()=>{const s=o=>{o.target&&!r.current&&vm(Hx,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Xd(){const e=new CustomEvent(Ba);document.dispatchEvent(e)}function vm(e,t,n,{discrete:r}){const s=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?mm(s,o):s.dispatchEvent(o)}var Kx=nc,qx=ym,rr=globalThis!=null&&globalThis.document?w.useLayoutEffect:()=>{},Yx="Portal",xm=w.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[s,o]=w.useState(!1);rr(()=>o(!0),[]);const i=n||s&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?pv.createPortal(l.jsx(Ue.div,{...r,ref:t}),i):null});xm.displayName=Yx;function Xx(e,t){return w.useReducer((n,r)=>t[n][r]??n,e)}var rc=e=>{const{present:t,children:n}=e,r=Zx(t),s=typeof n=="function"?n({present:r.isPresent}):w.Children.only(n),o=mt(r.ref,Jx(s));return typeof n=="function"||r.isPresent?w.cloneElement(s,{ref:o}):null};rc.displayName="Presence";function Zx(e){const[t,n]=w.useState(),r=w.useRef({}),s=w.useRef(e),o=w.useRef("none"),i=e?"mounted":"unmounted",[a,u]=Xx(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const c=_o(r.current);o.current=a==="mounted"?c:"none"},[a]),rr(()=>{const c=r.current,d=s.current;if(d!==e){const m=o.current,y=_o(c);e?u("MOUNT"):y==="none"||(c==null?void 0:c.display)==="none"?u("UNMOUNT"):u(d&&m!==y?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,u]),rr(()=>{if(t){let c;const d=t.ownerDocument.defaultView??window,f=y=>{const x=_o(r.current).includes(y.animationName);if(y.target===t&&x&&(u("ANIMATION_END"),!s.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",c=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},m=y=>{y.target===t&&(o.current=_o(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(c),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:w.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function _o(e){return(e==null?void 0:e.animationName)||"none"}function Jx(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function e1({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,s]=t1({defaultProp:t,onChange:n}),o=e!==void 0,i=o?e:r,a=Pt(n),u=w.useCallback(c=>{if(o){const f=typeof c=="function"?c(e):c;f!==e&&a(f)}else s(c)},[o,e,s,a]);return[i,u]}function t1({defaultProp:e,onChange:t}){const n=w.useState(e),[r]=n,s=w.useRef(r),o=Pt(t);return w.useEffect(()=>{s.current!==r&&(o(r),s.current=r)},[r,s,o]),n}var n1="VisuallyHidden",Ki=w.forwardRef((e,t)=>l.jsx(Ue.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ki.displayName=n1;var r1=Ki,sc="ToastProvider",[oc,s1,o1]=zx("Toast"),[wm,xb]=Gi("Toast",[o1]),[i1,qi]=wm(sc),bm=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:s="right",swipeThreshold:o=50,children:i}=e,[a,u]=w.useState(null),[c,d]=w.useState(0),f=w.useRef(!1),m=w.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${sc}\`. Expected non-empty \`string\`.`),l.jsx(oc.Provider,{scope:t,children:l.jsx(i1,{scope:t,label:n,duration:r,swipeDirection:s,swipeThreshold:o,toastCount:c,viewport:a,onViewportChange:u,onToastAdd:w.useCallback(()=>d(y=>y+1),[]),onToastRemove:w.useCallback(()=>d(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:m,children:i})})};bm.displayName=sc;var Sm="ToastViewport",l1=["F8"],Ha="toast.viewportPause",Wa="toast.viewportResume",Nm=w.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=l1,label:s="Notifications ({hotkey})",...o}=e,i=qi(Sm,n),a=s1(n),u=w.useRef(null),c=w.useRef(null),d=w.useRef(null),f=w.useRef(null),m=mt(t,f,i.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=i.toastCount>0;w.useEffect(()=>{const b=g=>{var h;r.length!==0&&r.every(N=>g[N]||g.code===N)&&((h=f.current)==null||h.focus())};return document.addEventListener("keydown",b),()=>document.removeEventListener("keydown",b)},[r]),w.useEffect(()=>{const b=u.current,g=f.current;if(v&&b&&g){const p=()=>{if(!i.isClosePausedRef.current){const C=new CustomEvent(Ha);g.dispatchEvent(C),i.isClosePausedRef.current=!0}},h=()=>{if(i.isClosePausedRef.current){const C=new CustomEvent(Wa);g.dispatchEvent(C),i.isClosePausedRef.current=!1}},N=C=>{!b.contains(C.relatedTarget)&&h()},k=()=>{b.contains(document.activeElement)||h()};return b.addEventListener("focusin",p),b.addEventListener("focusout",N),b.addEventListener("pointermove",p),b.addEventListener("pointerleave",k),window.addEventListener("blur",p),window.addEventListener("focus",h),()=>{b.removeEventListener("focusin",p),b.removeEventListener("focusout",N),b.removeEventListener("pointermove",p),b.removeEventListener("pointerleave",k),window.removeEventListener("blur",p),window.removeEventListener("focus",h)}}},[v,i.isClosePausedRef]);const x=w.useCallback(({tabbingDirection:b})=>{const p=a().map(h=>{const N=h.ref.current,k=[N,...w1(N)];return b==="forwards"?k:k.reverse()});return(b==="forwards"?p.reverse():p).flat()},[a]);return w.useEffect(()=>{const b=f.current;if(b){const g=p=>{var k,C,j;const h=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!h){const E=document.activeElement,L=p.shiftKey;if(p.target===b&&L){(k=c.current)==null||k.focus();return}const I=x({tabbingDirection:L?"backwards":"forwards"}),O=I.findIndex(T=>T===E);_l(I.slice(O+1))?p.preventDefault():L?(C=c.current)==null||C.focus():(j=d.current)==null||j.focus()}};return b.addEventListener("keydown",g),()=>b.removeEventListener("keydown",g)}},[a,x]),l.jsxs(qx,{ref:u,role:"region","aria-label":s.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&l.jsx(Qa,{ref:c,onFocusFromOutsideViewport:()=>{const b=x({tabbingDirection:"forwards"});_l(b)}}),l.jsx(oc.Slot,{scope:n,children:l.jsx(Ue.ol,{tabIndex:-1,...o,ref:m})}),v&&l.jsx(Qa,{ref:d,onFocusFromOutsideViewport:()=>{const b=x({tabbingDirection:"backwards"});_l(b)}})]})});Nm.displayName=Sm;var km="ToastFocusProxy",Qa=w.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...s}=e,o=qi(km,n);return l.jsx(Ki,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:i=>{var c;const a=i.relatedTarget;!((c=o.viewport)!=null&&c.contains(a))&&r()}})});Qa.displayName=km;var Yi="Toast",a1="toast.swipeStart",u1="toast.swipeMove",c1="toast.swipeCancel",d1="toast.swipeEnd",jm=w.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:s,onOpenChange:o,...i}=e,[a=!0,u]=e1({prop:r,defaultProp:s,onChange:o});return l.jsx(rc,{present:n||a,children:l.jsx(p1,{open:a,...i,ref:t,onClose:()=>u(!1),onPause:Pt(e.onPause),onResume:Pt(e.onResume),onSwipeStart:pe(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:pe(e.onSwipeMove,c=>{const{x:d,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${d}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${f}px`)}),onSwipeCancel:pe(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:pe(e.onSwipeEnd,c=>{const{x:d,y:f}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${d}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${f}px`),u(!1)})})})});jm.displayName=Yi;var[f1,h1]=wm(Yi,{onClose(){}}),p1=w.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:s,open:o,onClose:i,onEscapeKeyDown:a,onPause:u,onResume:c,onSwipeStart:d,onSwipeMove:f,onSwipeCancel:m,onSwipeEnd:y,...v}=e,x=qi(Yi,n),[b,g]=w.useState(null),p=mt(t,T=>g(T)),h=w.useRef(null),N=w.useRef(null),k=s||x.duration,C=w.useRef(0),j=w.useRef(k),E=w.useRef(0),{onToastAdd:L,onToastRemove:_}=x,U=Pt(()=>{var H;(b==null?void 0:b.contains(document.activeElement))&&((H=x.viewport)==null||H.focus()),i()}),I=w.useCallback(T=>{!T||T===1/0||(window.clearTimeout(E.current),C.current=new Date().getTime(),E.current=window.setTimeout(U,T))},[U]);w.useEffect(()=>{const T=x.viewport;if(T){const H=()=>{I(j.current),c==null||c()},$=()=>{const G=new Date().getTime()-C.current;j.current=j.current-G,window.clearTimeout(E.current),u==null||u()};return T.addEventListener(Ha,$),T.addEventListener(Wa,H),()=>{T.removeEventListener(Ha,$),T.removeEventListener(Wa,H)}}},[x.viewport,k,u,c,I]),w.useEffect(()=>{o&&!x.isClosePausedRef.current&&I(k)},[o,k,x.isClosePausedRef,I]),w.useEffect(()=>(L(),()=>_()),[L,_]);const O=w.useMemo(()=>b?Lm(b):null,[b]);return x.viewport?l.jsxs(l.Fragment,{children:[O&&l.jsx(m1,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:O}),l.jsx(f1,{scope:n,onClose:U,children:uo.createPortal(l.jsx(oc.ItemSlot,{scope:n,children:l.jsx(Kx,{asChild:!0,onEscapeKeyDown:pe(a,()=>{x.isFocusedToastEscapeKeyDownRef.current||U(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(Ue.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":o?"open":"closed","data-swipe-direction":x.swipeDirection,...v,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:pe(e.onKeyDown,T=>{T.key==="Escape"&&(a==null||a(T.nativeEvent),T.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:pe(e.onPointerDown,T=>{T.button===0&&(h.current={x:T.clientX,y:T.clientY})}),onPointerMove:pe(e.onPointerMove,T=>{if(!h.current)return;const H=T.clientX-h.current.x,$=T.clientY-h.current.y,G=!!N.current,P=["left","right"].includes(x.swipeDirection),M=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,F=P?M(0,H):0,W=P?0:M(0,$),re=T.pointerType==="touch"?10:2,ot={x:F,y:W},Ye={originalEvent:T,delta:ot};G?(N.current=ot,Mo(u1,f,Ye,{discrete:!1})):Zd(ot,x.swipeDirection,re)?(N.current=ot,Mo(a1,d,Ye,{discrete:!1}),T.target.setPointerCapture(T.pointerId)):(Math.abs(H)>re||Math.abs($)>re)&&(h.current=null)}),onPointerUp:pe(e.onPointerUp,T=>{const H=N.current,$=T.target;if($.hasPointerCapture(T.pointerId)&&$.releasePointerCapture(T.pointerId),N.current=null,h.current=null,H){const G=T.currentTarget,P={originalEvent:T,delta:H};Zd(H,x.swipeDirection,x.swipeThreshold)?Mo(d1,y,P,{discrete:!0}):Mo(c1,m,P,{discrete:!0}),G.addEventListener("click",M=>M.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),m1=e=>{const{__scopeToast:t,children:n,...r}=e,s=qi(Yi,t),[o,i]=w.useState(!1),[a,u]=w.useState(!1);return v1(()=>i(!0)),w.useEffect(()=>{const c=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:l.jsx(xm,{asChild:!0,children:l.jsx(Ki,{...r,children:o&&l.jsxs(l.Fragment,{children:[s.label," ",n]})})})},g1="ToastTitle",Cm=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(Ue.div,{...r,ref:t})});Cm.displayName=g1;var y1="ToastDescription",Em=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(Ue.div,{...r,ref:t})});Em.displayName=y1;var Pm="ToastAction",Tm=w.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?l.jsx(Om,{altText:n,asChild:!0,children:l.jsx(ic,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${Pm}\`. Expected non-empty \`string\`.`),null)});Tm.displayName=Pm;var Rm="ToastClose",ic=w.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,s=h1(Rm,n);return l.jsx(Om,{asChild:!0,children:l.jsx(Ue.button,{type:"button",...r,ref:t,onClick:pe(e.onClick,s.onClose)})})});ic.displayName=Rm;var Om=w.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...s}=e;return l.jsx(Ue.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...s,ref:t})});function Lm(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),x1(r)){const s=r.ariaHidden||r.hidden||r.style.display==="none",o=r.dataset.radixToastAnnounceExclude==="";if(!s)if(o){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...Lm(r))}}),t}function Mo(e,t,n,{discrete:r}){const s=n.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?mm(s,o):s.dispatchEvent(o)}var Zd=(e,t,n=0)=>{const r=Math.abs(e.x),s=Math.abs(e.y),o=r>s;return t==="left"||t==="right"?o&&r>n:!o&&s>n};function v1(e=()=>{}){const t=Pt(e);rr(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function x1(e){return e.nodeType===e.ELEMENT_NODE}function w1(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function _l(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var b1=bm,_m=Nm,Mm=jm,Am=Cm,Dm=Em,Im=Tm,Fm=ic;function zm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=zm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Um(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=zm(e))&&(r&&(r+=" "),r+=t);return r}const Jd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ef=Um,$m=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return ef(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:o}=t,i=Object.keys(s).map(c=>{const d=n==null?void 0:n[c],f=o==null?void 0:o[c];if(d===null)return null;const m=Jd(d)||Jd(f);return s[c][m]}),a=n&&Object.entries(n).reduce((c,d)=>{let[f,m]=d;return m===void 0||(c[f]=m),c},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,d)=>{let{class:f,className:m,...y}=d;return Object.entries(y).every(v=>{let[x,b]=v;return Array.isArray(b)?b.includes({...o,...a}[x]):{...o,...a}[x]===b})?[...c,f,m]:c},[]);return ef(e,i,u,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Vm=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var N1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k1=w.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:o,iconNode:i,...a},u)=>w.createElement("svg",{ref:u,...N1,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Vm("lucide",s),...a},[...i.map(([c,d])=>w.createElement(c,d)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=(e,t)=>{const n=w.forwardRef(({className:r,...s},o)=>w.createElement(k1,{ref:o,iconNode:t,className:Vm(`lucide-${S1(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bm=Y("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j1=Y("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C1=Y("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hm=Y("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=Y("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kn=Y("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wm=Y("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tf=Y("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xo=Y("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=Y("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E1=Y("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const P1=Y("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qm=Y("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lc=Y("Gamepad",[["line",{x1:"6",x2:"10",y1:"12",y2:"12",key:"161bw2"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"15",x2:"15.01",y1:"13",y2:"13",key:"dqpgro"}],["line",{x1:"18",x2:"18.01",y1:"11",y2:"11",key:"meh2c"}],["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T1=Y("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ga=Y("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R1=Y("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O1=Y("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L1=Y("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nf=Y("MousePointer",[["path",{d:"M12.586 12.586 19 19",key:"ea5xo7"}],["path",{d:"M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z",key:"277e5u"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _1=Y("Newspaper",[["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2",key:"7pis2x"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M10 6h8v4h-8V6Z",key:"smlsk5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M1=Y("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rf=Y("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A1=Y("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D1=Y("Slice",[["path",{d:"m8 14-6 6h9v-3",key:"zo3j9a"}],["path",{d:"M18.37 3.63 8 14l3 3L21.37 6.63a2.12 2.12 0 1 0-3-3Z",key:"1dzx0j"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I1=Y("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F1=Y("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gm=Y("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z1=Y("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Km=Y("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U1=Y("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $1=Y("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ac="-",V1=e=>{const t=H1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(ac);return a[0]===""&&a.length!==1&&a.shift(),qm(a,t)||B1(i)},getConflictingClassGroupIds:(i,a)=>{const u=n[i]||[];return a&&r[i]?[...u,...r[i]]:u}}},qm=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?qm(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const o=e.join(ac);return(i=t.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},sf=/^\[(.+)\]$/,B1=e=>{if(sf.test(e)){const t=sf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},H1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Q1(Object.entries(e.classGroups),n).forEach(([o,i])=>{Ka(i,r,o,t)}),r},Ka=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const o=s===""?t:of(t,s);o.classGroupId=n;return}if(typeof s=="function"){if(W1(s)){Ka(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([o,i])=>{Ka(i,of(t,o),n,r)})})},of=(e,t)=>{let n=e;return t.split(ac).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},W1=e=>e.isThemeGetter,Q1=(e,t)=>t?e.map(([n,r])=>{const s=r.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,a])=>[t+i,a])):o);return[n,s]}):e,G1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return s(o,i),i},set(o,i){n.has(o)?n.set(o,i):s(o,i)}}},Ym="!",K1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],o=t.length,i=a=>{const u=[];let c=0,d=0,f;for(let b=0;b<a.length;b++){let g=a[b];if(c===0){if(g===s&&(r||a.slice(b,b+o)===t)){u.push(a.slice(d,b)),d=b+o;continue}if(g==="/"){f=b;continue}}g==="["?c++:g==="]"&&c--}const m=u.length===0?a:a.substring(d),y=m.startsWith(Ym),v=y?m.substring(1):m,x=f&&f>d?f-d:void 0;return{modifiers:u,hasImportantModifier:y,baseClassName:v,maybePostfixModifierPosition:x}};return n?a=>n({className:a,parseClassName:i}):i},q1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Y1=e=>({cache:G1(e.cacheSize),parseClassName:K1(e),...V1(e)}),X1=/\s+/,Z1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,o=[],i=e.trim().split(X1);let a="";for(let u=i.length-1;u>=0;u-=1){const c=i[u],{modifiers:d,hasImportantModifier:f,baseClassName:m,maybePostfixModifierPosition:y}=n(c);let v=!!y,x=r(v?m.substring(0,y):m);if(!x){if(!v){a=c+(a.length>0?" "+a:a);continue}if(x=r(m),!x){a=c+(a.length>0?" "+a:a);continue}v=!1}const b=q1(d).join(":"),g=f?b+Ym:b,p=g+x;if(o.includes(p))continue;o.push(p);const h=s(x,v);for(let N=0;N<h.length;++N){const k=h[N];o.push(g+k)}a=c+(a.length>0?" "+a:a)}return a};function J1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Xm(t))&&(r&&(r+=" "),r+=n);return r}const Xm=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Xm(e[r]))&&(n&&(n+=" "),n+=t);return n};function ew(e,...t){let n,r,s,o=i;function i(u){const c=t.reduce((d,f)=>f(d),e());return n=Y1(c),r=n.cache.get,s=n.cache.set,o=a,a(u)}function a(u){const c=r(u);if(c)return c;const d=Z1(u,n);return s(u,d),d}return function(){return o(J1.apply(null,arguments))}}const ee=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Zm=/^\[(?:([a-z-]+):)?(.+)\]$/i,tw=/^\d+\/\d+$/,nw=new Set(["px","full","screen"]),rw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ow=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,iw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,lw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Lt=e=>Pr(e)||nw.has(e)||tw.test(e),Yt=e=>rs(e,"length",mw),Pr=e=>!!e&&!Number.isNaN(Number(e)),Ml=e=>rs(e,"number",Pr),gs=e=>!!e&&Number.isInteger(Number(e)),aw=e=>e.endsWith("%")&&Pr(e.slice(0,-1)),V=e=>Zm.test(e),Xt=e=>rw.test(e),uw=new Set(["length","size","percentage"]),cw=e=>rs(e,uw,Jm),dw=e=>rs(e,"position",Jm),fw=new Set(["image","url"]),hw=e=>rs(e,fw,yw),pw=e=>rs(e,"",gw),ys=()=>!0,rs=(e,t,n)=>{const r=Zm.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},mw=e=>sw.test(e)&&!ow.test(e),Jm=()=>!1,gw=e=>iw.test(e),yw=e=>lw.test(e),vw=()=>{const e=ee("colors"),t=ee("spacing"),n=ee("blur"),r=ee("brightness"),s=ee("borderColor"),o=ee("borderRadius"),i=ee("borderSpacing"),a=ee("borderWidth"),u=ee("contrast"),c=ee("grayscale"),d=ee("hueRotate"),f=ee("invert"),m=ee("gap"),y=ee("gradientColorStops"),v=ee("gradientColorStopPositions"),x=ee("inset"),b=ee("margin"),g=ee("opacity"),p=ee("padding"),h=ee("saturate"),N=ee("scale"),k=ee("sepia"),C=ee("skew"),j=ee("space"),E=ee("translate"),L=()=>["auto","contain","none"],_=()=>["auto","hidden","clip","visible","scroll"],U=()=>["auto",V,t],I=()=>[V,t],O=()=>["",Lt,Yt],T=()=>["auto",Pr,V],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],M=()=>["","0",V],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>[Pr,V];return{cacheSize:500,separator:":",theme:{colors:[ys],spacing:[Lt,Yt],blur:["none","",Xt,V],brightness:W(),borderColor:[e],borderRadius:["none","","full",Xt,V],borderSpacing:I(),borderWidth:O(),contrast:W(),grayscale:M(),hueRotate:W(),invert:M(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[aw,Yt],inset:U(),margin:U(),opacity:W(),padding:I(),saturate:W(),scale:W(),sepia:M(),skew:W(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",V]}],container:["container"],columns:[{columns:[Xt]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),V]}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",gs,V]}],basis:[{basis:U()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",V]}],grow:[{grow:M()}],shrink:[{shrink:M()}],order:[{order:["first","last","none",gs,V]}],"grid-cols":[{"grid-cols":[ys]}],"col-start-end":[{col:["auto",{span:["full",gs,V]},V]}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":[ys]}],"row-start-end":[{row:["auto",{span:[gs,V]},V]}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",V]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",V]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",V,t]}],"min-w":[{"min-w":[V,t,"min","max","fit"]}],"max-w":[{"max-w":[V,t,"none","full","min","max","fit","prose",{screen:[Xt]},Xt]}],h:[{h:[V,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[V,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Xt,Yt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Ml]}],"font-family":[{font:[ys]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",V]}],"line-clamp":[{"line-clamp":["none",Pr,Ml]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Lt,V]}],"list-image":[{"list-image":["none",V]}],"list-style-type":[{list:["none","disc","decimal",V]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Lt,Yt]}],"underline-offset":[{"underline-offset":["auto",Lt,V]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),dw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",cw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},hw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[v]}],"gradient-via-pos":[{via:[v]}],"gradient-to-pos":[{to:[v]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:$()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[Lt,V]}],"outline-w":[{outline:[Lt,Yt]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Lt,Yt]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Xt,pw]}],"shadow-color":[{shadow:[ys]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",Xt,V]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[h]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[h]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",V]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",V]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",V]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[N]}],"scale-x":[{"scale-x":[N]}],"scale-y":[{"scale-y":[N]}],rotate:[{rotate:[gs,V]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",V]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Lt,Yt,Ml]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},xw=ew(vw);function Le(...e){return xw(Um(e))}const ww=b1,e0=w.forwardRef(({className:e,...t},n)=>l.jsx(_m,{ref:n,className:Le("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));e0.displayName=_m.displayName;const bw=$m("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),t0=w.forwardRef(({className:e,variant:t,...n},r)=>l.jsx(Mm,{ref:r,className:Le(bw({variant:t}),e),...n}));t0.displayName=Mm.displayName;const Sw=w.forwardRef(({className:e,...t},n)=>l.jsx(Im,{ref:n,className:Le("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Sw.displayName=Im.displayName;const n0=w.forwardRef(({className:e,...t},n)=>l.jsx(Fm,{ref:n,className:Le("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:l.jsx(U1,{className:"h-4 w-4"})}));n0.displayName=Fm.displayName;const r0=w.forwardRef(({className:e,...t},n)=>l.jsx(Am,{ref:n,className:Le("text-sm font-semibold",e),...t}));r0.displayName=Am.displayName;const s0=w.forwardRef(({className:e,...t},n)=>l.jsx(Dm,{ref:n,className:Le("text-sm opacity-90",e),...t}));s0.displayName=Dm.displayName;function Nw(){const{toasts:e}=fm();return l.jsxs(ww,{children:[e.map(function({id:t,title:n,description:r,action:s,...o}){return l.jsxs(t0,{...o,children:[l.jsxs("div",{className:"grid gap-1",children:[n&&l.jsx(r0,{children:n}),r&&l.jsx(s0,{children:r})]}),s,l.jsx(n0,{})]},t)}),l.jsx(e0,{})]})}const kw=["top","right","bottom","left"],En=Math.min,Be=Math.max,Ci=Math.round,Ao=Math.floor,Et=e=>({x:e,y:e}),jw={left:"right",right:"left",bottom:"top",top:"bottom"},Cw={start:"end",end:"start"};function qa(e,t,n){return Be(e,En(t,n))}function Wt(e,t){return typeof e=="function"?e(t):e}function Qt(e){return e.split("-")[0]}function ss(e){return e.split("-")[1]}function uc(e){return e==="x"?"y":"x"}function cc(e){return e==="y"?"height":"width"}function Pn(e){return["top","bottom"].includes(Qt(e))?"y":"x"}function dc(e){return uc(Pn(e))}function Ew(e,t,n){n===void 0&&(n=!1);const r=ss(e),s=dc(e),o=cc(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=Ei(i)),[i,Ei(i)]}function Pw(e){const t=Ei(e);return[Ya(e),t,Ya(t)]}function Ya(e){return e.replace(/start|end/g,t=>Cw[t])}function Tw(e,t,n){const r=["left","right"],s=["right","left"],o=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:r:t?r:s;case"left":case"right":return t?o:i;default:return[]}}function Rw(e,t,n,r){const s=ss(e);let o=Tw(Qt(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(Ya)))),o}function Ei(e){return e.replace(/left|right|bottom|top/g,t=>jw[t])}function Ow(e){return{top:0,right:0,bottom:0,left:0,...e}}function o0(e){return typeof e!="number"?Ow(e):{top:e,right:e,bottom:e,left:e}}function Pi(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function lf(e,t,n){let{reference:r,floating:s}=e;const o=Pn(t),i=dc(t),a=cc(i),u=Qt(t),c=o==="y",d=r.x+r.width/2-s.width/2,f=r.y+r.height/2-s.height/2,m=r[a]/2-s[a]/2;let y;switch(u){case"top":y={x:d,y:r.y-s.height};break;case"bottom":y={x:d,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:f};break;case"left":y={x:r.x-s.width,y:f};break;default:y={x:r.x,y:r.y}}switch(ss(t)){case"start":y[i]-=m*(n&&c?-1:1);break;case"end":y[i]+=m*(n&&c?-1:1);break}return y}const Lw=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:d,y:f}=lf(c,r,u),m=r,y={},v=0;for(let x=0;x<a.length;x++){const{name:b,fn:g}=a[x],{x:p,y:h,data:N,reset:k}=await g({x:d,y:f,initialPlacement:r,placement:m,strategy:s,middlewareData:y,rects:c,platform:i,elements:{reference:e,floating:t}});d=p??d,f=h??f,y={...y,[b]:{...y[b],...N}},k&&v<=50&&(v++,typeof k=="object"&&(k.placement&&(m=k.placement),k.rects&&(c=k.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):k.rects),{x:d,y:f}=lf(c,m,u)),x=-1)}return{x:d,y:f,placement:m,strategy:s,middlewareData:y}};async function Zs(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:m=!1,padding:y=0}=Wt(t,e),v=o0(y),b=a[m?f==="floating"?"reference":"floating":f],g=Pi(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(b)))==null||n?b:b.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:u})),p=f==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,h=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),N=await(o.isElement==null?void 0:o.isElement(h))?await(o.getScale==null?void 0:o.getScale(h))||{x:1,y:1}:{x:1,y:1},k=Pi(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:p,offsetParent:h,strategy:u}):p);return{top:(g.top-k.top+v.top)/N.y,bottom:(k.bottom-g.bottom+v.bottom)/N.y,left:(g.left-k.left+v.left)/N.x,right:(k.right-g.right+v.right)/N.x}}const _w=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:u}=t,{element:c,padding:d=0}=Wt(e,t)||{};if(c==null)return{};const f=o0(d),m={x:n,y:r},y=dc(s),v=cc(y),x=await i.getDimensions(c),b=y==="y",g=b?"top":"left",p=b?"bottom":"right",h=b?"clientHeight":"clientWidth",N=o.reference[v]+o.reference[y]-m[y]-o.floating[v],k=m[y]-o.reference[y],C=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let j=C?C[h]:0;(!j||!await(i.isElement==null?void 0:i.isElement(C)))&&(j=a.floating[h]||o.floating[v]);const E=N/2-k/2,L=j/2-x[v]/2-1,_=En(f[g],L),U=En(f[p],L),I=_,O=j-x[v]-U,T=j/2-x[v]/2+E,H=qa(I,T,O),$=!u.arrow&&ss(s)!=null&&T!==H&&o.reference[v]/2-(T<I?_:U)-x[v]/2<0,G=$?T<I?T-I:T-O:0;return{[y]:m[y]+G,data:{[y]:H,centerOffset:T-H-G,...$&&{alignmentOffset:G}},reset:$}}}),Mw=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:u,elements:c}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:m,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:x=!0,...b}=Wt(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const g=Qt(s),p=Pn(a),h=Qt(a)===a,N=await(u.isRTL==null?void 0:u.isRTL(c.floating)),k=m||(h||!x?[Ei(a)]:Pw(a)),C=v!=="none";!m&&C&&k.push(...Rw(a,x,v,N));const j=[a,...k],E=await Zs(t,b),L=[];let _=((r=o.flip)==null?void 0:r.overflows)||[];if(d&&L.push(E[g]),f){const T=Ew(s,i,N);L.push(E[T[0]],E[T[1]])}if(_=[..._,{placement:s,overflows:L}],!L.every(T=>T<=0)){var U,I;const T=(((U=o.flip)==null?void 0:U.index)||0)+1,H=j[T];if(H)return{data:{index:T,overflows:_},reset:{placement:H}};let $=(I=_.filter(G=>G.overflows[0]<=0).sort((G,P)=>G.overflows[1]-P.overflows[1])[0])==null?void 0:I.placement;if(!$)switch(y){case"bestFit":{var O;const G=(O=_.filter(P=>{if(C){const M=Pn(P.placement);return M===p||M==="y"}return!0}).map(P=>[P.placement,P.overflows.filter(M=>M>0).reduce((M,F)=>M+F,0)]).sort((P,M)=>P[1]-M[1])[0])==null?void 0:O[0];G&&($=G);break}case"initialPlacement":$=a;break}if(s!==$)return{reset:{placement:$}}}return{}}}};function af(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function uf(e){return kw.some(t=>e[t]>=0)}const Aw=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Wt(e,t);switch(r){case"referenceHidden":{const o=await Zs(t,{...s,elementContext:"reference"}),i=af(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:uf(i)}}}case"escaped":{const o=await Zs(t,{...s,altBoundary:!0}),i=af(o,n.floating);return{data:{escapedOffsets:i,escaped:uf(i)}}}default:return{}}}}};async function Dw(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=Qt(n),a=ss(n),u=Pn(n)==="y",c=["left","top"].includes(i)?-1:1,d=o&&u?-1:1,f=Wt(t,e);let{mainAxis:m,crossAxis:y,alignmentAxis:v}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof v=="number"&&(y=a==="end"?v*-1:v),u?{x:y*d,y:m*c}:{x:m*c,y:y*d}}const Iw=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,u=await Dw(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+u.x,y:o+u.y,data:{...u,placement:i}}}}},Fw=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:b=>{let{x:g,y:p}=b;return{x:g,y:p}}},...u}=Wt(e,t),c={x:n,y:r},d=await Zs(t,u),f=Pn(Qt(s)),m=uc(f);let y=c[m],v=c[f];if(o){const b=m==="y"?"top":"left",g=m==="y"?"bottom":"right",p=y+d[b],h=y-d[g];y=qa(p,y,h)}if(i){const b=f==="y"?"top":"left",g=f==="y"?"bottom":"right",p=v+d[b],h=v-d[g];v=qa(p,v,h)}const x=a.fn({...t,[m]:y,[f]:v});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[m]:o,[f]:i}}}}}},zw=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:o,middlewareData:i}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=Wt(e,t),d={x:n,y:r},f=Pn(s),m=uc(f);let y=d[m],v=d[f];const x=Wt(a,t),b=typeof x=="number"?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(u){const h=m==="y"?"height":"width",N=o.reference[m]-o.floating[h]+b.mainAxis,k=o.reference[m]+o.reference[h]-b.mainAxis;y<N?y=N:y>k&&(y=k)}if(c){var g,p;const h=m==="y"?"width":"height",N=["top","left"].includes(Qt(s)),k=o.reference[f]-o.floating[h]+(N&&((g=i.offset)==null?void 0:g[f])||0)+(N?0:b.crossAxis),C=o.reference[f]+o.reference[h]+(N?0:((p=i.offset)==null?void 0:p[f])||0)-(N?b.crossAxis:0);v<k?v=k:v>C&&(v=C)}return{[m]:y,[f]:v}}}},Uw=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:o,platform:i,elements:a}=t,{apply:u=()=>{},...c}=Wt(e,t),d=await Zs(t,c),f=Qt(s),m=ss(s),y=Pn(s)==="y",{width:v,height:x}=o.floating;let b,g;f==="top"||f==="bottom"?(b=f,g=m===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(g=f,b=m==="end"?"top":"bottom");const p=x-d.top-d.bottom,h=v-d.left-d.right,N=En(x-d[b],p),k=En(v-d[g],h),C=!t.middlewareData.shift;let j=N,E=k;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(E=h),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(j=p),C&&!m){const _=Be(d.left,0),U=Be(d.right,0),I=Be(d.top,0),O=Be(d.bottom,0);y?E=v-2*(_!==0||U!==0?_+U:Be(d.left,d.right)):j=x-2*(I!==0||O!==0?I+O:Be(d.top,d.bottom))}await u({...t,availableWidth:E,availableHeight:j});const L=await i.getDimensions(a.floating);return v!==L.width||x!==L.height?{reset:{rects:!0}}:{}}}};function Xi(){return typeof window<"u"}function os(e){return i0(e)?(e.nodeName||"").toLowerCase():"#document"}function Qe(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Rt(e){var t;return(t=(i0(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function i0(e){return Xi()?e instanceof Node||e instanceof Qe(e).Node:!1}function gt(e){return Xi()?e instanceof Element||e instanceof Qe(e).Element:!1}function Tt(e){return Xi()?e instanceof HTMLElement||e instanceof Qe(e).HTMLElement:!1}function cf(e){return!Xi()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Qe(e).ShadowRoot}function fo(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=yt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(s)}function $w(e){return["table","td","th"].includes(os(e))}function Zi(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function fc(e){const t=hc(),n=gt(e)?yt(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Vw(e){let t=Tn(e);for(;Tt(t)&&!Xr(t);){if(fc(t))return t;if(Zi(t))return null;t=Tn(t)}return null}function hc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Xr(e){return["html","body","#document"].includes(os(e))}function yt(e){return Qe(e).getComputedStyle(e)}function Ji(e){return gt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Tn(e){if(os(e)==="html")return e;const t=e.assignedSlot||e.parentNode||cf(e)&&e.host||Rt(e);return cf(t)?t.host:t}function l0(e){const t=Tn(e);return Xr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Tt(t)&&fo(t)?t:l0(t)}function Js(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=l0(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=Qe(s);if(o){const a=Xa(i);return t.concat(i,i.visualViewport||[],fo(s)?s:[],a&&n?Js(a):[])}return t.concat(s,Js(s,[],n))}function Xa(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function a0(e){const t=yt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Tt(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=Ci(n)!==o||Ci(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function pc(e){return gt(e)?e:e.contextElement}function Tr(e){const t=pc(e);if(!Tt(t))return Et(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=a0(t);let i=(o?Ci(n.width):n.width)/r,a=(o?Ci(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Bw=Et(0);function u0(e){const t=Qe(e);return!hc()||!t.visualViewport?Bw:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Hw(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Qe(e)?!1:t}function sr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=pc(e);let i=Et(1);t&&(r?gt(r)&&(i=Tr(r)):i=Tr(e));const a=Hw(o,n,r)?u0(o):Et(0);let u=(s.left+a.x)/i.x,c=(s.top+a.y)/i.y,d=s.width/i.x,f=s.height/i.y;if(o){const m=Qe(o),y=r&&gt(r)?Qe(r):r;let v=m,x=Xa(v);for(;x&&r&&y!==v;){const b=Tr(x),g=x.getBoundingClientRect(),p=yt(x),h=g.left+(x.clientLeft+parseFloat(p.paddingLeft))*b.x,N=g.top+(x.clientTop+parseFloat(p.paddingTop))*b.y;u*=b.x,c*=b.y,d*=b.x,f*=b.y,u+=h,c+=N,v=Qe(x),x=Xa(v)}}return Pi({width:d,height:f,x:u,y:c})}function mc(e,t){const n=Ji(e).scrollLeft;return t?t.left+n:sr(Rt(e)).left+n}function c0(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:mc(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function Ww(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=Rt(r),a=t?Zi(t.floating):!1;if(r===i||a&&o)return n;let u={scrollLeft:0,scrollTop:0},c=Et(1);const d=Et(0),f=Tt(r);if((f||!f&&!o)&&((os(r)!=="body"||fo(i))&&(u=Ji(r)),Tt(r))){const y=sr(r);c=Tr(r),d.x=y.x+r.clientLeft,d.y=y.y+r.clientTop}const m=i&&!f&&!o?c0(i,u,!0):Et(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+m.x,y:n.y*c.y-u.scrollTop*c.y+d.y+m.y}}function Qw(e){return Array.from(e.getClientRects())}function Gw(e){const t=Rt(e),n=Ji(e),r=e.ownerDocument.body,s=Be(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=Be(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+mc(e);const a=-n.scrollTop;return yt(r).direction==="rtl"&&(i+=Be(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function Kw(e,t){const n=Qe(e),r=Rt(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,u=0;if(s){o=s.width,i=s.height;const c=hc();(!c||c&&t==="fixed")&&(a=s.offsetLeft,u=s.offsetTop)}return{width:o,height:i,x:a,y:u}}function qw(e,t){const n=sr(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=Tt(e)?Tr(e):Et(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,u=s*o.x,c=r*o.y;return{width:i,height:a,x:u,y:c}}function df(e,t,n){let r;if(t==="viewport")r=Kw(e,n);else if(t==="document")r=Gw(Rt(e));else if(gt(t))r=qw(t,n);else{const s=u0(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return Pi(r)}function d0(e,t){const n=Tn(e);return n===t||!gt(n)||Xr(n)?!1:yt(n).position==="fixed"||d0(n,t)}function Yw(e,t){const n=t.get(e);if(n)return n;let r=Js(e,[],!1).filter(a=>gt(a)&&os(a)!=="body"),s=null;const o=yt(e).position==="fixed";let i=o?Tn(e):e;for(;gt(i)&&!Xr(i);){const a=yt(i),u=fc(i);!u&&a.position==="fixed"&&(s=null),(o?!u&&!s:!u&&a.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||fo(i)&&!u&&d0(e,i))?r=r.filter(d=>d!==i):s=a,i=Tn(i)}return t.set(e,r),r}function Xw(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Zi(t)?[]:Yw(t,this._c):[].concat(n),r],a=i[0],u=i.reduce((c,d)=>{const f=df(t,d,s);return c.top=Be(f.top,c.top),c.right=En(f.right,c.right),c.bottom=En(f.bottom,c.bottom),c.left=Be(f.left,c.left),c},df(t,a,s));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Zw(e){const{width:t,height:n}=a0(e);return{width:t,height:n}}function Jw(e,t,n){const r=Tt(t),s=Rt(t),o=n==="fixed",i=sr(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const u=Et(0);if(r||!r&&!o)if((os(t)!=="body"||fo(s))&&(a=Ji(t)),r){const m=sr(t,!0,o,t);u.x=m.x+t.clientLeft,u.y=m.y+t.clientTop}else s&&(u.x=mc(s));const c=s&&!r&&!o?c0(s,a):Et(0),d=i.left+a.scrollLeft-u.x-c.x,f=i.top+a.scrollTop-u.y-c.y;return{x:d,y:f,width:i.width,height:i.height}}function Al(e){return yt(e).position==="static"}function ff(e,t){if(!Tt(e)||yt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Rt(e)===n&&(n=n.ownerDocument.body),n}function f0(e,t){const n=Qe(e);if(Zi(e))return n;if(!Tt(e)){let s=Tn(e);for(;s&&!Xr(s);){if(gt(s)&&!Al(s))return s;s=Tn(s)}return n}let r=ff(e,t);for(;r&&$w(r)&&Al(r);)r=ff(r,t);return r&&Xr(r)&&Al(r)&&!fc(r)?n:r||Vw(e)||n}const e2=async function(e){const t=this.getOffsetParent||f0,n=this.getDimensions,r=await n(e.floating);return{reference:Jw(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function t2(e){return yt(e).direction==="rtl"}const n2={convertOffsetParentRelativeRectToViewportRelativeRect:Ww,getDocumentElement:Rt,getClippingRect:Xw,getOffsetParent:f0,getElementRects:e2,getClientRects:Qw,getDimensions:Zw,getScale:Tr,isElement:gt,isRTL:t2};function h0(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function r2(e,t){let n=null,r;const s=Rt(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,u){a===void 0&&(a=!1),u===void 0&&(u=1),o();const c=e.getBoundingClientRect(),{left:d,top:f,width:m,height:y}=c;if(a||t(),!m||!y)return;const v=Ao(f),x=Ao(s.clientWidth-(d+m)),b=Ao(s.clientHeight-(f+y)),g=Ao(d),h={rootMargin:-v+"px "+-x+"px "+-b+"px "+-g+"px",threshold:Be(0,En(1,u))||1};let N=!0;function k(C){const j=C[0].intersectionRatio;if(j!==u){if(!N)return i();j?i(!1,j):r=setTimeout(()=>{i(!1,1e-7)},1e3)}j===1&&!h0(c,e.getBoundingClientRect())&&i(),N=!1}try{n=new IntersectionObserver(k,{...h,root:s.ownerDocument})}catch{n=new IntersectionObserver(k,h)}n.observe(e)}return i(!0),o}function s2(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,c=pc(e),d=s||o?[...c?Js(c):[],...Js(t)]:[];d.forEach(g=>{s&&g.addEventListener("scroll",n,{passive:!0}),o&&g.addEventListener("resize",n)});const f=c&&a?r2(c,n):null;let m=-1,y=null;i&&(y=new ResizeObserver(g=>{let[p]=g;p&&p.target===c&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var h;(h=y)==null||h.observe(t)})),n()}),c&&!u&&y.observe(c),y.observe(t));let v,x=u?sr(e):null;u&&b();function b(){const g=sr(e);x&&!h0(x,g)&&n(),x=g,v=requestAnimationFrame(b)}return n(),()=>{var g;d.forEach(p=>{s&&p.removeEventListener("scroll",n),o&&p.removeEventListener("resize",n)}),f==null||f(),(g=y)==null||g.disconnect(),y=null,u&&cancelAnimationFrame(v)}}const o2=Iw,i2=Fw,l2=Mw,a2=Uw,u2=Aw,hf=_w,c2=zw,d2=(e,t,n)=>{const r=new Map,s={platform:n2,...n},o={...s.platform,_c:r};return Lw(e,t,{...s,platform:o})};var Zo=typeof document<"u"?w.useLayoutEffect:w.useEffect;function Ti(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ti(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&e.$$typeof)&&!Ti(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function p0(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function pf(e,t){const n=p0(e);return Math.round(t*n)/n}function Dl(e){const t=w.useRef(e);return Zo(()=>{t.current=e}),t}function f2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:i}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[d,f]=w.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,y]=w.useState(r);Ti(m,r)||y(r);const[v,x]=w.useState(null),[b,g]=w.useState(null),p=w.useCallback(P=>{P!==C.current&&(C.current=P,x(P))},[]),h=w.useCallback(P=>{P!==j.current&&(j.current=P,g(P))},[]),N=o||v,k=i||b,C=w.useRef(null),j=w.useRef(null),E=w.useRef(d),L=u!=null,_=Dl(u),U=Dl(s),I=Dl(c),O=w.useCallback(()=>{if(!C.current||!j.current)return;const P={placement:t,strategy:n,middleware:m};U.current&&(P.platform=U.current),d2(C.current,j.current,P).then(M=>{const F={...M,isPositioned:I.current!==!1};T.current&&!Ti(E.current,F)&&(E.current=F,uo.flushSync(()=>{f(F)}))})},[m,t,n,U,I]);Zo(()=>{c===!1&&E.current.isPositioned&&(E.current.isPositioned=!1,f(P=>({...P,isPositioned:!1})))},[c]);const T=w.useRef(!1);Zo(()=>(T.current=!0,()=>{T.current=!1}),[]),Zo(()=>{if(N&&(C.current=N),k&&(j.current=k),N&&k){if(_.current)return _.current(N,k,O);O()}},[N,k,O,_,L]);const H=w.useMemo(()=>({reference:C,floating:j,setReference:p,setFloating:h}),[p,h]),$=w.useMemo(()=>({reference:N,floating:k}),[N,k]),G=w.useMemo(()=>{const P={position:n,left:0,top:0};if(!$.floating)return P;const M=pf($.floating,d.x),F=pf($.floating,d.y);return a?{...P,transform:"translate("+M+"px, "+F+"px)",...p0($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:F}},[n,a,$.floating,d.x,d.y]);return w.useMemo(()=>({...d,update:O,refs:H,elements:$,floatingStyles:G}),[d,O,H,$,G])}const h2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?hf({element:r.current,padding:s}).fn(n):{}:r?hf({element:r,padding:s}).fn(n):{}}}},p2=(e,t)=>({...o2(e),options:[e,t]}),m2=(e,t)=>({...i2(e),options:[e,t]}),g2=(e,t)=>({...c2(e),options:[e,t]}),y2=(e,t)=>({...l2(e),options:[e,t]}),v2=(e,t)=>({...a2(e),options:[e,t]}),x2=(e,t)=>({...u2(e),options:[e,t]}),w2=(e,t)=>({...h2(e),options:[e,t]});var b2="Arrow",m0=w.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...o}=e;return l.jsx(Ue.svg,{...o,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});m0.displayName=b2;var S2=m0;function N2(e){const[t,n]=w.useState(void 0);return rr(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const o=s[0];let i,a;if("borderBoxSize"in o){const u=o.borderBoxSize,c=Array.isArray(u)?u[0]:u;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var g0="Popper",[y0,v0]=Gi(g0),[wb,x0]=y0(g0),w0="PopperAnchor",b0=w.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,o=x0(w0,n),i=w.useRef(null),a=mt(t,i);return w.useEffect(()=>{o.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:l.jsx(Ue.div,{...s,ref:a})});b0.displayName=w0;var gc="PopperContent",[k2,j2]=y0(gc),S0=w.forwardRef((e,t)=>{var _n,vc,xc,wc,bc,Sc;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:o="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:v,...x}=e,b=x0(gc,n),[g,p]=w.useState(null),h=mt(t,ls=>p(ls)),[N,k]=w.useState(null),C=N2(N),j=(C==null?void 0:C.width)??0,E=(C==null?void 0:C.height)??0,L=r+(o!=="center"?"-"+o:""),_=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},U=Array.isArray(c)?c:[c],I=U.length>0,O={padding:_,boundary:U.filter(E2),altBoundary:I},{refs:T,floatingStyles:H,placement:$,isPositioned:G,middlewareData:P}=f2({strategy:"fixed",placement:L,whileElementsMounted:(...ls)=>s2(...ls,{animationFrame:y==="always"}),elements:{reference:b.anchor},middleware:[p2({mainAxis:s+E,alignmentAxis:i}),u&&m2({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?g2():void 0,...O}),u&&y2({...O}),v2({...O,apply:({elements:ls,rects:Nc,availableWidth:I0,availableHeight:F0})=>{const{width:z0,height:U0}=Nc.reference,ho=ls.floating.style;ho.setProperty("--radix-popper-available-width",`${I0}px`),ho.setProperty("--radix-popper-available-height",`${F0}px`),ho.setProperty("--radix-popper-anchor-width",`${z0}px`),ho.setProperty("--radix-popper-anchor-height",`${U0}px`)}}),N&&w2({element:N,padding:a}),P2({arrowWidth:j,arrowHeight:E}),m&&x2({strategy:"referenceHidden",...O})]}),[M,F]=j0($),W=Pt(v);rr(()=>{G&&(W==null||W())},[G,W]);const re=(_n=P.arrow)==null?void 0:_n.x,ot=(vc=P.arrow)==null?void 0:vc.y,Ye=((xc=P.arrow)==null?void 0:xc.centerOffset)!==0,[is,Ot]=w.useState();return rr(()=>{g&&Ot(window.getComputedStyle(g).zIndex)},[g]),l.jsx("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:G?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:is,"--radix-popper-transform-origin":[(wc=P.transformOrigin)==null?void 0:wc.x,(bc=P.transformOrigin)==null?void 0:bc.y].join(" "),...((Sc=P.hide)==null?void 0:Sc.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:l.jsx(k2,{scope:n,placedSide:M,onArrowChange:k,arrowX:re,arrowY:ot,shouldHideArrow:Ye,children:l.jsx(Ue.div,{"data-side":M,"data-align":F,...x,ref:h,style:{...x.style,animation:G?void 0:"none"}})})})});S0.displayName=gc;var N0="PopperArrow",C2={top:"bottom",right:"left",bottom:"top",left:"right"},k0=w.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,o=j2(N0,r),i=C2[o.placedSide];return l.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:l.jsx(S2,{...s,ref:n,style:{...s.style,display:"block"}})})});k0.displayName=N0;function E2(e){return e!==null}var P2=e=>({name:"transformOrigin",options:e,fn(t){var b,g,p;const{placement:n,rects:r,middlewareData:s}=t,i=((b=s.arrow)==null?void 0:b.centerOffset)!==0,a=i?0:e.arrowWidth,u=i?0:e.arrowHeight,[c,d]=j0(n),f={start:"0%",center:"50%",end:"100%"}[d],m=(((g=s.arrow)==null?void 0:g.x)??0)+a/2,y=(((p=s.arrow)==null?void 0:p.y)??0)+u/2;let v="",x="";return c==="bottom"?(v=i?f:`${m}px`,x=`${-u}px`):c==="top"?(v=i?f:`${m}px`,x=`${r.floating.height+u}px`):c==="right"?(v=`${-u}px`,x=i?f:`${y}px`):c==="left"&&(v=`${r.floating.width+u}px`,x=i?f:`${y}px`),{data:{x:v,y:x}}}});function j0(e){const[t,n="center"]=e.split("-");return[t,n]}var T2=b0,R2=S0,O2=k0,[el,bb]=Gi("Tooltip",[v0]),yc=v0(),C0="TooltipProvider",L2=700,mf="tooltip.open",[_2,E0]=el(C0),P0=e=>{const{__scopeTooltip:t,delayDuration:n=L2,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:o}=e,i=w.useRef(!0),a=w.useRef(!1),u=w.useRef(0);return w.useEffect(()=>{const c=u.current;return()=>window.clearTimeout(c)},[]),l.jsx(_2,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:w.useCallback(()=>{window.clearTimeout(u.current),i.current=!1},[]),onClose:w.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:w.useCallback(c=>{a.current=c},[]),disableHoverableContent:s,children:o})};P0.displayName=C0;var T0="Tooltip",[Sb,tl]=el(T0),Za="TooltipTrigger",M2=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=tl(Za,n),o=E0(Za,n),i=yc(n),a=w.useRef(null),u=mt(t,a,s.onTriggerChange),c=w.useRef(!1),d=w.useRef(!1),f=w.useCallback(()=>c.current=!1,[]);return w.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),l.jsx(T2,{asChild:!0,...i,children:l.jsx(Ue.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:u,onPointerMove:pe(e.onPointerMove,m=>{m.pointerType!=="touch"&&!d.current&&!o.isPointerInTransitRef.current&&(s.onTriggerEnter(),d.current=!0)}),onPointerLeave:pe(e.onPointerLeave,()=>{s.onTriggerLeave(),d.current=!1}),onPointerDown:pe(e.onPointerDown,()=>{s.open&&s.onClose(),c.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:pe(e.onFocus,()=>{c.current||s.onOpen()}),onBlur:pe(e.onBlur,s.onClose),onClick:pe(e.onClick,s.onClose)})})});M2.displayName=Za;var A2="TooltipPortal",[Nb,D2]=el(A2,{forceMount:void 0}),Zr="TooltipContent",R0=w.forwardRef((e,t)=>{const n=D2(Zr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:s="top",...o}=e,i=tl(Zr,e.__scopeTooltip);return l.jsx(rc,{present:r||i.open,children:i.disableHoverableContent?l.jsx(O0,{side:s,...o,ref:t}):l.jsx(I2,{side:s,...o,ref:t})})}),I2=w.forwardRef((e,t)=>{const n=tl(Zr,e.__scopeTooltip),r=E0(Zr,e.__scopeTooltip),s=w.useRef(null),o=mt(t,s),[i,a]=w.useState(null),{trigger:u,onClose:c}=n,d=s.current,{onPointerInTransitChange:f}=r,m=w.useCallback(()=>{a(null),f(!1)},[f]),y=w.useCallback((v,x)=>{const b=v.currentTarget,g={x:v.clientX,y:v.clientY},p=V2(g,b.getBoundingClientRect()),h=B2(g,p),N=H2(x.getBoundingClientRect()),k=Q2([...h,...N]);a(k),f(!0)},[f]);return w.useEffect(()=>()=>m(),[m]),w.useEffect(()=>{if(u&&d){const v=b=>y(b,d),x=b=>y(b,u);return u.addEventListener("pointerleave",v),d.addEventListener("pointerleave",x),()=>{u.removeEventListener("pointerleave",v),d.removeEventListener("pointerleave",x)}}},[u,d,y,m]),w.useEffect(()=>{if(i){const v=x=>{const b=x.target,g={x:x.clientX,y:x.clientY},p=(u==null?void 0:u.contains(b))||(d==null?void 0:d.contains(b)),h=!W2(g,i);p?m():h&&(m(),c())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[u,d,i,c,m]),l.jsx(O0,{...e,ref:o})}),[F2,z2]=el(T0,{isInside:!1}),U2=Ax("TooltipContent"),O0=w.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=e,u=tl(Zr,n),c=yc(n),{onClose:d}=u;return w.useEffect(()=>(document.addEventListener(mf,d),()=>document.removeEventListener(mf,d)),[d]),w.useEffect(()=>{if(u.trigger){const f=m=>{const y=m.target;y!=null&&y.contains(u.trigger)&&d()};return window.addEventListener("scroll",f,{capture:!0}),()=>window.removeEventListener("scroll",f,{capture:!0})}},[u.trigger,d]),l.jsx(nc,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:f=>f.preventDefault(),onDismiss:d,children:l.jsxs(R2,{"data-state":u.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(U2,{children:r}),l.jsx(F2,{scope:n,isInside:!0,children:l.jsx(r1,{id:u.contentId,role:"tooltip",children:s||r})})]})})});R0.displayName=Zr;var L0="TooltipArrow",$2=w.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=yc(n);return z2(L0,n).isInside?null:l.jsx(O2,{...s,...r,ref:t})});$2.displayName=L0;function V2(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,s,o)){case o:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function B2(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function H2(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function W2(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o].x,u=t[o].y,c=t[i].x,d=t[i].y;u>r!=d>r&&n<(c-a)*(r-u)/(d-u)+a&&(s=!s)}return s}function Q2(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),G2(t)}function G2(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var K2=P0,_0=R0;const q2=K2,Y2=w.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(_0,{ref:r,sideOffset:t,className:Le("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...n}));Y2.displayName=_0.displayName;function X2(){const e=t=>{const n=document.getElementById(t);n&&n.scrollIntoView({behavior:"smooth"})};return l.jsx("nav",{className:"glassmorphism relative z-50",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"flex items-center justify-between h-16",children:[l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(lc,{className:"text-white text-2xl"}),l.jsx("span",{className:"text-white text-xl font-bold",children:"GameHub"})]}),l.jsx("div",{className:"hidden md:block",children:l.jsxs("div",{className:"ml-10 flex items-baseline space-x-8",children:[l.jsx("button",{onClick:()=>e("news"),className:"text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium",children:"News"}),l.jsx("button",{onClick:()=>e("deals"),className:"text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium",children:"Deals"}),l.jsx("button",{onClick:()=>e("shortener"),className:"text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium",children:"Link Shortener"}),l.jsxs("button",{onClick:()=>e("shortener"),className:"flex items-center text-white hover:text-pink-400 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium",children:[l.jsx(Xo,{className:"w-4 h-4 mr-1"}),"Start Earning"]}),l.jsx(Hv,{href:"/dashboard",children:l.jsxs("a",{className:"flex items-center bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 text-sm font-medium",children:[l.jsx(Km,{className:"w-4 h-4 mr-1"}),"Dashboard"]})})]})}),l.jsx("div",{className:"md:hidden",children:l.jsx("button",{type:"button",className:"text-white hover:text-pink-400",children:l.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]})})})}function Z2(){const e=t=>{const n=document.getElementById(t);n&&n.scrollIntoView({behavior:"smooth"})};return l.jsxs("section",{className:"relative overflow-hidden py-20 px-4",children:[l.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[l.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-white mb-6 leading-tight",children:["Your Ultimate ",l.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-white",children:"Gaming Hub"})]}),l.jsx("p",{className:"text-xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed",children:"Stay updated with the latest gaming news, discover amazing deals on gaming products, and shorten your links with our powerful tools."}),l.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[l.jsxs("button",{onClick:()=>e("deals"),className:"bg-pink-500 hover:bg-pink-600 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 hover:scale-105 shadow-lg flex items-center justify-center",children:[l.jsx(Qm,{className:"mr-2 h-5 w-5"}),"View Hot Deals"]}),l.jsxs("button",{onClick:()=>e("news"),className:"glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center",children:[l.jsx(_1,{className:"mr-2 h-5 w-5"}),"Latest News"]})]})]}),l.jsx("div",{className:"absolute top-20 left-10 animate-bounce-slow",children:l.jsx(Gm,{className:"text-yellow-400 text-3xl opacity-30 h-8 w-8"})}),l.jsx("div",{className:"absolute top-40 right-20 animate-pulse-glow",children:l.jsx(L1,{className:"text-pink-400 text-2xl opacity-40 h-6 w-6"})}),l.jsx("div",{className:"absolute bottom-20 left-20 animate-bounce-slow",style:{animationDelay:"1s"},children:l.jsx(I1,{className:"text-yellow-300 text-xl opacity-30 h-5 w-5"})})]})}function J2(){const{data:e,isLoading:t,error:n}=tc({queryKey:["/api/news"]});return n?l.jsx("section",{id:"news",className:"py-16 px-4",children:l.jsx("div",{className:"max-w-7xl mx-auto text-center",children:l.jsx("p",{className:"text-red-400",children:"Failed to load news. Please try again later."})})}):l.jsx("section",{id:"news",className:"py-16 px-4",children:l.jsxs("div",{className:"max-w-7xl mx-auto",children:[l.jsxs("div",{className:"flex items-center justify-between mb-12",children:[l.jsxs("div",{children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-2",children:"Latest Gaming News"}),l.jsx("p",{className:"text-gray-300",children:"Stay informed with the latest happenings in gaming"})]}),l.jsxs("div",{className:"hidden md:block text-sm text-gray-300",children:[l.jsx(M1,{className:"inline mr-2 h-4 w-4"}),"Updates Automatically"]})]}),t?l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3].map(r=>l.jsxs("div",{className:"glassmorphism rounded-xl p-6 animate-pulse",children:[l.jsx("div",{className:"w-full h-48 bg-white bg-opacity-20 rounded-lg mb-4"}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("div",{className:"w-20 h-6 bg-white bg-opacity-20 rounded-full"}),l.jsx("div",{className:"w-full h-6 bg-white bg-opacity-20 rounded"}),l.jsx("div",{className:"w-full h-12 bg-white bg-opacity-20 rounded"}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("div",{className:"w-16 h-4 bg-white bg-opacity-20 rounded"}),l.jsx("div",{className:"w-16 h-4 bg-white bg-opacity-20 rounded"})]})]})]},r))}):l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e==null?void 0:e.map(r=>l.jsxs("article",{className:"glassmorphism rounded-xl p-6 hover-lift cursor-pointer",children:[l.jsx("img",{src:r.imageUrl,alt:r.title,className:"w-full h-48 object-cover rounded-lg mb-4"}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("span",{className:"inline-block bg-purple-600 text-white text-xs px-3 py-1 rounded-full font-medium",children:r.category}),l.jsx("h3",{className:"text-xl font-semibold text-white leading-tight",children:r.title}),l.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:r.excerpt}),l.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[l.jsx("span",{children:r.date}),l.jsx("span",{children:r.readTime})]})]})]},r.id))}),l.jsx("div",{className:"text-center mt-10",children:l.jsxs("button",{className:"glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200",children:[l.jsx("span",{className:"mr-2",children:"+"}),"Load More News"]})})]})})}function eb(){const{data:e,isLoading:t,error:n}=tc({queryKey:["/api/deals"]});return n?l.jsx("section",{id:"deals",className:"py-16 px-4",children:l.jsx("div",{className:"max-w-7xl mx-auto text-center",children:l.jsx("p",{className:"text-red-400",children:"Failed to load deals. Please try again later."})})}):l.jsx("section",{id:"deals",className:"py-16 px-4",children:l.jsxs("div",{className:"max-w-7xl mx-auto",children:[l.jsxs("div",{className:"flex items-center justify-between mb-12",children:[l.jsxs("div",{children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-2",children:"🔥 Hot Gaming Deals"}),l.jsx("p",{className:"text-gray-300",children:"Discover incredible savings on your favorite games and gear"})]}),l.jsxs("div",{className:"hidden md:block text-sm text-gray-300",children:[l.jsx(kn,{className:"inline mr-2 h-4 w-4"}),"Limited Time Offers"]})]}),t?l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[1,2,3,4].map(r=>l.jsxs("div",{className:"glassmorphism rounded-xl p-6 animate-pulse",children:[l.jsx("div",{className:"w-full h-40 bg-white bg-opacity-20 rounded-lg mb-4"}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("div",{className:"w-full h-6 bg-white bg-opacity-20 rounded"}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("div",{className:"w-20 h-8 bg-white bg-opacity-20 rounded"}),l.jsx("div",{className:"w-16 h-6 bg-white bg-opacity-20 rounded"})]}),l.jsxs("div",{className:"flex justify-between",children:[l.jsx("div",{className:"w-16 h-6 bg-white bg-opacity-20 rounded-full"}),l.jsx("div",{className:"w-16 h-4 bg-white bg-opacity-20 rounded"})]}),l.jsx("div",{className:"w-full h-8 bg-white bg-opacity-20 rounded-lg"})]})]},r))}):l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e==null?void 0:e.map(r=>l.jsxs("div",{className:"glassmorphism rounded-xl p-6 hover-lift group",children:[l.jsx("img",{src:r.imageUrl,alt:r.title,className:"w-full h-40 object-cover rounded-lg mb-4 group-hover:scale-105 transition-transform duration-200"}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("h3",{className:"text-lg font-semibold text-white",children:r.title}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("span",{className:"text-2xl font-bold text-green-400",children:r.currentPrice}),l.jsx("span",{className:"text-sm text-gray-400 line-through",children:r.originalPrice})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold",children:r.discount}),l.jsx("span",{className:"text-xs text-gray-400",children:r.timeLeft})]}),l.jsxs("button",{className:"w-full bg-pink-500 hover:bg-pink-600 text-white font-semibold py-2 rounded-lg transition-colors duration-200 flex items-center justify-center",children:[l.jsx(A1,{className:"mr-2 h-4 w-4"}),"Get Deal"]})]})]},r.id))}),l.jsx("div",{className:"text-center mt-10",children:l.jsxs("button",{className:"glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center mx-auto",children:[l.jsx(Qm,{className:"mr-2 h-5 w-5"}),"View All Deals"]})})]})})}const tb=$m("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Gt=w.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},o)=>{const i=r?_x:"button";return l.jsx(i,{className:Le(tb({variant:t,size:n,className:e})),ref:o,...s})});Gt.displayName="Button";const M0=w.forwardRef(({className:e,type:t,...n},r)=>l.jsx("input",{type:t,className:Le("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));M0.displayName="Input";function nb(){const[e,t]=w.useState(""),[n,r]=w.useState(""),[s,o]=w.useState(!1),{toast:i}=fm(),a=ec(),{data:u,isLoading:c}=tc({queryKey:["/api/links/recent"]}),d=Nx({mutationFn:async v=>(await kx("POST","/api/links/shorten",{originalUrl:v})).json(),onSuccess:v=>{r(v.shortUrl),o(!0),t(""),a.invalidateQueries({queryKey:["/api/links/recent"]}),i({title:"Link shortened successfully!",description:"Your link has been shortened and is ready to share."})},onError:v=>{let x="Failed to shorten the link. Please try again.";if(v.message)if(v.message.includes("400:")){const b=v.message.match(/400:\s*(.+)/);b&&(x=b[1])}else x=v.message;i({title:"Error",description:x,variant:"destructive"})}}),f=v=>{if(v.preventDefault(),!e.trim()){i({title:"Error",description:"Please enter a URL to shorten.",variant:"destructive"});return}try{new URL(e),d.mutate(e)}catch{i({title:"Error",description:"Please enter a valid URL.",variant:"destructive"})}},m=async()=>{try{await navigator.clipboard.writeText(n),i({title:"Copied!",description:"Link copied to clipboard."})}catch{i({title:"Error",description:"Failed to copy link to clipboard.",variant:"destructive"})}},y=v=>new Date(v).toLocaleDateString();return l.jsx("section",{id:"shortener",className:"py-16 px-4",children:l.jsxs("div",{className:"max-w-4xl mx-auto",children:[l.jsxs("div",{className:"text-center mb-12",children:[l.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"🔗 Link Shortener"}),l.jsx("p",{className:"text-gray-300 text-lg",children:"Shorten your gaming links and track their performance"})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-8 mb-8",children:l.jsxs("form",{onSubmit:f,className:"space-y-6",children:[l.jsxs("div",{className:"relative",children:[l.jsx(M0,{type:"url",placeholder:"Enter your long URL here...",value:e,onChange:v=>t(v.target.value),className:"w-full bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg px-4 py-4 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200"}),l.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-4",children:l.jsx(Ga,{className:"h-5 w-5 text-gray-400"})})]}),l.jsxs(Gt,{type:"submit",disabled:d.isPending,className:"w-full bg-pink-500 hover:bg-pink-600 text-white font-semibold py-4 rounded-lg transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",children:[l.jsx(D1,{className:"mr-2 h-5 w-5"}),d.isPending?"Shortening...":"Shorten Link"]}),s&&n&&l.jsx("div",{className:"bg-green-500 bg-opacity-20 border border-green-500 border-opacity-30 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex-1",children:[l.jsx("p",{className:"text-sm text-gray-300 mb-1",children:"Shortened URL:"}),l.jsx("p",{className:"text-white font-mono text-lg break-all",children:n})]}),l.jsx(Gt,{onClick:m,className:"ml-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:l.jsx(Wm,{className:"h-4 w-4"})})]})})]})}),l.jsxs("div",{className:"glassmorphism rounded-xl p-8",children:[l.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"📊 Recent Links"}),c?l.jsx("div",{className:"space-y-4",children:[1,2,3].map(v=>l.jsx("div",{className:"animate-pulse p-4 bg-white bg-opacity-5 rounded-lg",children:l.jsxs("div",{className:"flex justify-between",children:[l.jsxs("div",{className:"flex-1 space-y-2",children:[l.jsx("div",{className:"w-3/4 h-4 bg-white bg-opacity-20 rounded"}),l.jsx("div",{className:"w-1/2 h-3 bg-white bg-opacity-20 rounded"})]}),l.jsxs("div",{className:"flex space-x-6",children:[l.jsx("div",{className:"w-12 h-8 bg-white bg-opacity-20 rounded"}),l.jsx("div",{className:"w-16 h-4 bg-white bg-opacity-20 rounded"})]})]})},v))}):u&&u.length>0?l.jsx("div",{className:"space-y-4",children:u.map(v=>l.jsxs("div",{className:"flex items-center justify-between p-4 bg-white bg-opacity-5 rounded-lg hover:bg-opacity-10 transition-all duration-200",children:[l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-white font-medium truncate",children:v.shortUrl}),l.jsx("p",{className:"text-gray-400 text-sm truncate",children:v.originalUrl})]}),l.jsxs("div",{className:"flex items-center space-x-6 ml-4",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("p",{className:"text-2xl font-bold text-pink-400",children:v.clicks.toLocaleString()}),l.jsx("p",{className:"text-xs text-gray-400",children:"clicks"})]}),l.jsx("div",{className:"text-center",children:l.jsx("p",{className:"text-sm text-gray-300",children:y(v.createdAt)})})]})]},v.id))}):l.jsx("div",{className:"text-center py-8",children:l.jsx("p",{className:"text-gray-400",children:"No shortened links yet. Create your first one above!"})}),l.jsx("div",{className:"text-center mt-6",children:l.jsxs("button",{className:"text-pink-400 hover:text-pink-300 font-medium transition-colors duration-200 flex items-center justify-center mx-auto",children:[l.jsx(C1,{className:"mr-2 h-5 w-5"}),"View Detailed Analytics"]})})]})]})})}function rb(){return l.jsx("footer",{className:"glassmorphism mt-16",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 py-8",children:l.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-4 md:mb-0",children:[l.jsx(lc,{className:"text-white text-2xl"}),l.jsx("span",{className:"text-white text-xl font-bold",children:"GameHub"})]}),l.jsx("div",{className:"flex items-center space-x-6 text-sm text-gray-300",children:l.jsx("span",{children:"© 2024 GameHub. Built with Next.js and Tailwind CSS."})}),l.jsxs("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[l.jsx("a",{href:"#",className:"text-gray-300 hover:text-pink-400 transition-colors duration-200",children:l.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})}),l.jsx("a",{href:"#",className:"text-gray-300 hover:text-pink-400 transition-colors duration-200",children:l.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{d:"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418Z"})})}),l.jsx("a",{href:"#",className:"text-gray-300 hover:text-pink-400 transition-colors duration-200",children:l.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})})]})]})})})}function sb(){return l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600",children:[l.jsx(X2,{}),l.jsx(Z2,{}),l.jsx(J2,{}),l.jsx(eb,{}),l.jsx(nb,{}),l.jsx(rb,{})]})}function ob(){qu();const[e,t]=w.useState(20),[n,r]=w.useState(!1),[s,o]=w.useState(10),[i,a]=w.useState(!1),[u,c]=w.useState(!1),d=new URLSearchParams(window.location.search).get("code")||"";w.useEffect(()=>{const y=document.createElement("script");y.src="https://fpyf8.com/88/tag.min.js",y.setAttribute("data-zone","156349"),y.async=!0,y.setAttribute("data-cfasync","false"),document.body.appendChild(y);const v=document.createElement("script");return v.text="(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))",document.body.appendChild(v),()=>{document.body.contains(y)&&document.body.removeChild(y),document.body.contains(v)&&document.body.removeChild(v)}},[]),w.useEffect(()=>{const y=setInterval(()=>{t(v=>v<=1?(r(!0),0):v-1)},1e3);return()=>clearInterval(y)},[]),w.useEffect(()=>{const y=()=>{const v=window.scrollY,x=window.innerHeight;v>x*.5&&c(!0)};return window.addEventListener("scroll",y),()=>window.removeEventListener("scroll",y)},[]);const f=()=>{const y=setInterval(()=>{o(v=>v<=1?(a(!0),0):v-1)},1e3);setTimeout(()=>clearInterval(y),1e4)},m=()=>{if(u){const y=window.open(`/redirect-step2?code=${d}`,"_blank");y&&y.focus()}};return l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600",children:[l.jsx("nav",{className:"glassmorphism relative z-50",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsx("div",{className:"flex items-center justify-between h-16",children:l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(lc,{className:"text-white text-2xl"}),l.jsx("span",{className:"text-white text-xl font-bold",children:"Gaming News Hub"})]})})})}),e>0&&l.jsx("div",{className:"bg-red-600 text-white text-center py-3",children:l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(kn,{className:"h-5 w-5"}),l.jsxs("span",{className:"font-bold",children:["Please wait ",e," seconds..."]})]})}),l.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[l.jsxs("div",{className:"glassmorphism rounded-xl p-8 mb-8 text-center",children:[l.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"🎮 Latest Gaming News & Updates"}),l.jsx("p",{className:"text-gray-300 text-lg",children:"Stay ahead in the gaming world with our exclusive content"})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 mb-8 text-center",children:l.jsx("div",{id:"monetag-ad-zone-1",className:"min-h-[200px] flex items-center justify-center",children:l.jsx("div",{className:"text-gray-400 text-sm",children:"Loading content..."})})}),l.jsxs("div",{className:"space-y-8",children:[l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[l.jsx(Gm,{className:"text-yellow-400 h-6 w-6"}),l.jsx("span",{className:"text-pink-400 font-semibold",children:"Breaking News"})]}),l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Major Gaming Tournament Breaks All-Time Viewership Records"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Tournament",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"The latest gaming championship has shattered all previous viewership records, drawing over 50 million concurrent viewers worldwide. The tournament, featuring the biggest names in competitive gaming, showcased incredible skill and determination across multiple game titles."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Professional esports has evolved dramatically over the past decade, transforming from niche competitions to mainstream entertainment events that rival traditional sports in terms of audience engagement and prize pools."})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 text-center",children:l.jsx("div",{id:"monetag-ad-zone-2",className:"min-h-[200px] flex items-center justify-center",children:l.jsx("div",{className:"text-gray-400 text-sm",children:"Loading content..."})})}),l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[l.jsx(F1,{className:"text-blue-400 h-6 w-6"}),l.jsx("span",{className:"text-blue-400 font-semibold",children:"Industry Update"})]}),l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Next-Generation Gaming Hardware Revolutionizes Performance"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Hardware",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"The gaming industry continues to push the boundaries of what's possible with cutting-edge hardware releases that deliver unprecedented performance levels. From advanced graphics cards to lightning-fast processors, the latest technology is reshaping the gaming landscape."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"These technological advances are not just about raw power - they're enabling entirely new gaming experiences with ray tracing, high refresh rates, and immersive virtual reality that brings players closer to the action than ever before."})]}),n&&l.jsx("div",{className:"glassmorphism rounded-xl p-8 text-center",children:l.jsx(Gt,{onClick:f,className:"bg-pink-500 hover:bg-pink-600 text-white font-semibold py-3 px-8 rounded-lg text-lg",children:"Continue Reading"})}),s>0&&n&&l.jsxs("div",{className:"glassmorphism rounded-xl p-8 text-center",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[l.jsx(kn,{className:"h-5 w-5 text-pink-400"}),l.jsxs("span",{className:"text-white font-bold",children:["Please wait ",s," more seconds..."]})]}),l.jsx("div",{className:"animate-bounce",children:l.jsx(Bm,{className:"h-8 w-8 text-pink-400 mx-auto"})}),l.jsx("p",{className:"text-gray-300 mt-2",children:"Scroll down to continue"})]}),i&&u&&l.jsx("div",{className:"glassmorphism rounded-xl p-8 text-center",children:l.jsx(Gt,{onClick:m,className:"bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-8 rounded-lg text-lg",children:"Continue to Next Step"})}),l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Gaming Community Highlights"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Community",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"The gaming community continues to amaze with incredible creativity, from stunning fan art to innovative game modifications that extend the life of beloved titles. Community-driven content has become an integral part of the gaming ecosystem."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed",children:"Independent developers are also making their mark, creating unique gaming experiences that challenge conventional design and storytelling approaches, proving that innovation can come from anywhere in the gaming world."})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 text-center",children:l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})})})]})]})]})}function ib(){qu();const[e,t]=w.useState(10),[n,r]=w.useState(!1),[s,o]=w.useState(20),[i,a]=w.useState(!1),[u,c]=w.useState(!1),d=new URLSearchParams(window.location.search).get("code")||"";w.useEffect(()=>{const y=document.createElement("script");y.src="https://fpyf8.com/88/tag.min.js",y.setAttribute("data-zone","156349"),y.async=!0,y.setAttribute("data-cfasync","false"),document.body.appendChild(y);const v=document.createElement("script");return v.text="(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))",document.body.appendChild(v),()=>{document.body.contains(y)&&document.body.removeChild(y),document.body.contains(v)&&document.body.removeChild(v)}},[]),w.useEffect(()=>{const y=setInterval(()=>{t(v=>v<=1?(r(!0),0):v-1)},1e3);return()=>clearInterval(y)},[]),w.useEffect(()=>{const y=()=>{const v=window.scrollY,x=window.innerHeight;v>x*.5&&c(!0)};return window.addEventListener("scroll",y),()=>window.removeEventListener("scroll",y)},[]);const f=()=>{const y=setInterval(()=>{o(v=>v<=1?(a(!0),0):v-1)},1e3);setTimeout(()=>clearInterval(y),2e4)},m=()=>{if(u){const y=window.open(`/redirect-final?code=${d}`,"_blank");y&&y.focus()}};return l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600",children:[l.jsx("nav",{className:"glassmorphism relative z-50",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsx("div",{className:"flex items-center justify-between h-16",children:l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(j1,{className:"text-white text-2xl"}),l.jsx("span",{className:"text-white text-xl font-bold",children:"Gaming Career Hub"})]})})})}),e>0&&l.jsx("div",{className:"bg-blue-600 text-white text-center py-3",children:l.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[l.jsx(kn,{className:"h-5 w-5"}),l.jsxs("span",{className:"font-bold",children:["Please wait ",e," seconds..."]})]})}),l.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[l.jsxs("div",{className:"glassmorphism rounded-xl p-8 mb-8 text-center",children:[l.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"🚀 Build Your Gaming Career"}),l.jsx("p",{className:"text-gray-300 text-lg",children:"Transform your passion for gaming into a successful career"})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 mb-8 text-center",children:l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})})}),l.jsxs("div",{className:"space-y-8",children:[l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[l.jsx(T1,{className:"text-green-400 h-6 w-6"}),l.jsx("span",{className:"text-green-400 font-semibold",children:"Career Guide"})]}),l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"How to Start Your Professional Gaming Journey"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Professional Gaming",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Breaking into the professional gaming industry requires dedication, skill development, and strategic planning. Whether you want to become a competitive player, content creator, or work behind the scenes, there are multiple paths to success in the gaming world."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Professional gamers today earn substantial incomes through tournament winnings, sponsorships, streaming revenue, and brand partnerships. The industry has matured to offer stable career opportunities for talented individuals."})]}),n&&l.jsx("div",{className:"glassmorphism rounded-xl p-8 text-center",children:l.jsx(Gt,{onClick:f,className:"bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg text-lg",children:"Learn More About Gaming Careers"})}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 text-center",children:l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})})}),l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[l.jsx($1,{className:"text-yellow-400 h-6 w-6"}),l.jsx("span",{className:"text-yellow-400 font-semibold",children:"Success Stories"})]}),l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"From Bedroom to Big League: Gaming Success Stories"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1556075798-4825dfaaf498?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Success",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Many of today's top gaming professionals started as casual players who turned their hobby into lucrative careers. These success stories demonstrate that with the right approach, dedication, and timing, anyone can make it in the gaming industry."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Content creation has become one of the most accessible entry points into the gaming industry. Platforms like Twitch, YouTube, and TikTok have created opportunities for gamers to build audiences and monetize their skills and personality."})]}),s>0&&n&&l.jsxs("div",{className:"glassmorphism rounded-xl p-8 text-center",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[l.jsx(kn,{className:"h-5 w-5 text-blue-400"}),l.jsxs("span",{className:"text-white font-bold",children:["Please wait ",s," more seconds..."]})]}),l.jsx("div",{className:"animate-bounce",children:l.jsx(Bm,{className:"h-8 w-8 text-blue-400 mx-auto"})}),l.jsx("p",{className:"text-gray-300 mt-2",children:"Scroll down to continue"})]}),l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Skills Every Gaming Professional Needs"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Skills",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[l.jsxs("div",{className:"bg-white bg-opacity-10 rounded-lg p-6",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-3",children:"Technical Skills"}),l.jsxs("ul",{className:"text-gray-300 space-y-2",children:[l.jsx("li",{children:"• Game mastery and mechanical skill"}),l.jsx("li",{children:"• Understanding of game meta and strategies"}),l.jsx("li",{children:"• Stream setup and technical knowledge"}),l.jsx("li",{children:"• Video editing and content creation"})]})]}),l.jsxs("div",{className:"bg-white bg-opacity-10 rounded-lg p-6",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-3",children:"Soft Skills"}),l.jsxs("ul",{className:"text-gray-300 space-y-2",children:[l.jsx("li",{children:"• Communication and presentation"}),l.jsx("li",{children:"• Marketing and self-promotion"}),l.jsx("li",{children:"• Community management"}),l.jsx("li",{children:"• Business and financial planning"})]})]})]})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 text-center",children:l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})})}),i&&u&&l.jsx("div",{className:"glassmorphism rounded-xl p-8 text-center",children:l.jsx(Gt,{onClick:m,className:"bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-8 rounded-lg text-lg",children:"Continue to Final Step"})}),l.jsxs("article",{className:"glassmorphism rounded-xl p-8",children:[l.jsx("h2",{className:"text-3xl font-bold text-white mb-4",children:"Building Your Gaming Brand"}),l.jsx("img",{src:"https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",alt:"Gaming Brand",className:"w-full h-64 object-cover rounded-lg mb-6"}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed mb-6",children:"Personal branding is crucial in the gaming industry. Your brand represents your unique personality, gaming style, and the value you provide to your audience. Successful gaming professionals invest time in developing a consistent and authentic brand identity."}),l.jsx("p",{className:"text-gray-300 text-lg leading-relaxed",children:"Networking within the gaming community, collaborating with other creators, and staying up-to-date with industry trends are essential for long-term success in this rapidly evolving field."})]}),l.jsx("div",{className:"glassmorphism rounded-xl p-6 text-center",children:l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})})})]})]})]})}const lb="",In=e=>`${lb}${e}`;function ab(){const[e,t]=w.useState(20),[n,r]=w.useState(!1),[s,o]=w.useState(5),[i,a]=w.useState(!1),[u,c]=w.useState(""),d=new URLSearchParams(window.location.search).get("code")||"";w.useEffect(()=>{const y=document.createElement("script");y.src="https://fpyf8.com/88/tag.min.js",y.setAttribute("data-zone","156349"),y.async=!0,y.setAttribute("data-cfasync","false"),document.body.appendChild(y);const v=document.createElement("script");return v.text="(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))",document.body.appendChild(v),()=>{document.body.contains(y)&&document.body.removeChild(y),document.body.contains(v)&&document.body.removeChild(v)}},[]),w.useEffect(()=>{(async()=>{try{const x=await fetch(In(`/api/links/original/${d}`));if(x.ok){const b=await x.json();c(b.originalUrl)}}catch(x){console.error("Error fetching original link:",x)}})();const v=setInterval(()=>{t(x=>x<=1?(r(!0),0):x-1)},1e3);return()=>clearInterval(v)},[d]);const f=()=>{const y=setInterval(()=>{o(v=>v<=1?(a(!0),0):v-1)},1e3);setTimeout(()=>clearInterval(y),5e3)},m=async()=>{try{await fetch(`/s/${d}`),window.location.href=u}catch{window.location.href=u}};return l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600",children:[l.jsx("nav",{className:"glassmorphism relative z-50",children:l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsx("div",{className:"flex items-center justify-between h-16",children:l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx(rf,{className:"text-white text-2xl"}),l.jsx("span",{className:"text-white text-xl font-bold",children:"Secure Link Gateway"})]})})})}),l.jsx("div",{className:"max-w-3xl mx-auto px-4 py-16",children:l.jsxs("div",{className:"glassmorphism rounded-xl p-12 text-center",children:[l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[l.jsx(rf,{className:"h-12 w-12 text-green-400"}),l.jsx(ar,{className:"h-8 w-8 text-green-400"})]}),l.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"🔒 Secure Link Verification"}),l.jsx("p",{className:"text-gray-300 text-lg",children:"Your link is being processed through our security system"})]}),l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8 mb-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})}),e>0&&l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"bg-blue-600 bg-opacity-30 border border-blue-500 rounded-lg p-6 mb-6",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[l.jsx(kn,{className:"h-8 w-8 text-blue-400 animate-pulse"}),l.jsx("span",{className:"text-3xl font-bold text-white",children:e})]}),l.jsx("p",{className:"text-gray-300 text-lg",children:"Verifying link security and authenticity..."})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[l.jsxs("div",{className:"bg-white bg-opacity-10 rounded-lg p-4",children:[l.jsx(ar,{className:"h-6 w-6 text-green-400 mx-auto mb-2"}),l.jsx("p",{className:"text-white font-semibold",children:"Malware Scan"}),l.jsx("p",{className:"text-gray-300",children:"Complete"})]}),l.jsxs("div",{className:"bg-white bg-opacity-10 rounded-lg p-4",children:[l.jsx(ar,{className:"h-6 w-6 text-green-400 mx-auto mb-2"}),l.jsx("p",{className:"text-white font-semibold",children:"Spam Check"}),l.jsx("p",{className:"text-gray-300",children:"Verified"})]}),l.jsxs("div",{className:"bg-white bg-opacity-10 rounded-lg p-4",children:[l.jsx(kn,{className:"h-6 w-6 text-yellow-400 mx-auto mb-2 animate-pulse"}),l.jsx("p",{className:"text-white font-semibold",children:"Final Check"}),l.jsx("p",{className:"text-gray-300",children:"Processing..."})]})]})]}),n&&!i&&l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"bg-green-600 bg-opacity-30 border border-green-500 rounded-lg p-6 mb-6",children:[l.jsx(ar,{className:"h-12 w-12 text-green-400 mx-auto mb-4"}),l.jsx("p",{className:"text-green-400 text-xl font-bold mb-2",children:"✅ Security Check Complete"}),l.jsx("p",{className:"text-gray-300",children:"Your link has been verified and is safe to access"})]}),l.jsxs(Gt,{onClick:f,className:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-12 rounded-lg text-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[l.jsx(_s,{className:"mr-3 h-6 w-6"}),"Get Your Link"]})]}),s>0&&n&&!i&&l.jsx("div",{className:"mb-8",children:l.jsxs("div",{className:"bg-orange-600 bg-opacity-30 border border-orange-500 rounded-lg p-6",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[l.jsx(kn,{className:"h-6 w-6 text-orange-400"}),l.jsx("span",{className:"text-2xl font-bold text-white",children:s})]}),l.jsx("p",{className:"text-gray-300",children:"Preparing your link..."})]})}),i&&l.jsxs("div",{className:"mb-8",children:[l.jsxs("div",{className:"bg-purple-600 bg-opacity-30 border border-purple-500 rounded-lg p-6 mb-6",children:[l.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[l.jsx(_s,{className:"h-8 w-8 text-purple-400"}),l.jsx(ar,{className:"h-6 w-6 text-green-400"})]}),l.jsx("p",{className:"text-purple-400 text-xl font-bold mb-2",children:"🎉 Ready to Access Your Link!"}),l.jsx("p",{className:"text-gray-300",children:"Click the button below to be redirected to your destination"})]}),l.jsxs(Gt,{onClick:m,className:"bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-6 px-16 rounded-lg text-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 animate-pulse",children:[l.jsx(_s,{className:"mr-4 h-8 w-8"}),"Access Your Link Now"]})]}),l.jsx("div",{className:"bg-gray-700 bg-opacity-50 rounded-lg p-8 mt-8",children:l.jsx("p",{className:"text-gray-300",children:"Advertisement Space"})}),l.jsxs("div",{className:"mt-8 text-sm text-gray-400",children:[l.jsx("p",{className:"mb-2",children:"🔒 This link has been verified by our security system"}),l.jsx("p",{children:"⚡ Powered by GameHub Secure Gateway"})]})]})})]})}const A0=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));A0.displayName="Card";const ub=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("flex flex-col space-y-1.5 p-6",e),...t}));ub.displayName="CardHeader";const cb=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("text-2xl font-semibold leading-none tracking-tight",e),...t}));cb.displayName="CardTitle";const db=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("text-sm text-muted-foreground",e),...t}));db.displayName="CardDescription";const D0=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("p-6 pt-0",e),...t}));D0.displayName="CardContent";const fb=w.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,className:Le("flex items-center p-6 pt-0",e),...t}));fb.displayName="CardFooter";function hb(){return l.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:l.jsx(A0,{className:"w-full max-w-md mx-4",children:l.jsxs(D0,{className:"pt-6",children:[l.jsxs("div",{className:"flex mb-4 gap-2",children:[l.jsx(Hm,{className:"h-8 w-8 text-red-500"}),l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),l.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function gf({onLogin:e}){const[,t]=qu(),[n,r]=w.useState(!0),[s,o]=w.useState({username:"",password:""}),[i,a]=w.useState(!1),[u,c]=w.useState(!1),[d,f]=w.useState(""),m=async v=>{v.preventDefault(),c(!0),f("");try{const b=await fetch(In(n?"/api/auth/login":"/api/auth/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),g=await b.json();if(!b.ok)throw new Error(g.message||"Authentication failed");localStorage.setItem("token",g.token),localStorage.setItem("user",JSON.stringify(g.user)),e(g.user,g.token),t("/")}catch(x){f(x instanceof Error?x.message:"Authentication failed")}finally{c(!1)}},y=v=>{o(x=>({...x,[v.target.name]:v.target.value}))};return l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4",children:l.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 w-full max-w-md border border-white/20",children:[l.jsxs("div",{className:"text-center mb-8",children:[l.jsx("div",{className:"bg-gradient-to-r from-purple-400 to-pink-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(Km,{className:"w-8 h-8 text-white"})}),l.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:n?"Welcome Back":"Join & Earn"}),l.jsx("p",{className:"text-gray-300",children:n?"Sign in to your earning account":"Create account and start earning $0.007 per click!"})]}),l.jsxs("form",{onSubmit:m,className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),l.jsx("input",{type:"text",name:"username",value:s.username,onChange:y,required:!0,minLength:3,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent",placeholder:"Enter your username"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:i?"text":"password",name:"password",value:s.password,onChange:y,required:!0,minLength:6,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent pr-12",placeholder:"Enter your password"}),l.jsx("button",{type:"button",onClick:()=>a(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:i?l.jsx(E1,{className:"w-5 h-5"}):l.jsx(P1,{className:"w-5 h-5"})})]})]}),d&&l.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm",children:d}),l.jsx("button",{type:"submit",disabled:u,className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:u?l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),n?"Signing In...":"Creating Account..."]}):l.jsxs("div",{className:"flex items-center justify-center",children:[n?l.jsx(R1,{className:"w-5 h-5 mr-2"}):l.jsx(z1,{className:"w-5 h-5 mr-2"}),n?"Sign In":"Create Account"]})})]}),l.jsx("div",{className:"mt-6 text-center",children:l.jsx("button",{onClick:()=>{r(!n),f(""),o({username:"",password:""})},className:"text-purple-300 hover:text-purple-200 text-sm transition-colors",children:n?"Don't have an account? Sign up to start earning!":"Already have an account? Sign in"})}),!n&&l.jsxs("div",{className:"mt-6 bg-green-500/20 border border-green-500/50 rounded-lg p-4",children:[l.jsx("h3",{className:"text-green-200 font-medium mb-2",children:"💰 Start Earning Today!"}),l.jsxs("ul",{className:"text-green-300 text-sm space-y-1",children:[l.jsx("li",{children:"• Earn $0.007 per click on your links"}),l.jsx("li",{children:"• Minimum withdrawal: $4.00"}),l.jsx("li",{children:"• Multiple payment methods"}),l.jsx("li",{children:"• Real-time earnings tracking"})]})]})]})})}function pb({user:e,token:t,onLogout:n}){var U,I;if(console.log("Dashboard received user:",e),!e)return l.jsx("div",{className:"text-white p-4",children:"No user data received"});const[r,s]=w.useState(e),[o,i]=w.useState(""),[a,u]=w.useState(!1),[c,d]=w.useState(!1),[f,m]=w.useState(!1),[y,v]=w.useState([]),[x,b]=w.useState(""),[g,p]=w.useState(!1),[h,N]=w.useState({fullName:r.fullName||"",address:r.address||"",phoneNumber:r.phoneNumber||"",telegramUsername:r.telegramUsername||"",trafficSource:r.trafficSource||"",trafficSourceLinks:r.trafficSourceLinks||"",paymentMethod:r.paymentMethod||"",paymentDetails:r.paymentDetails||""});w.useEffect(()=>{k(),C()},[]);const k=async()=>{try{const O=await fetch(In("/api/auth/profile"),{headers:{Authorization:`Bearer ${t}`}});if(O.ok){const T=await O.json();s(T.user)}}catch(O){console.error("Error fetching user data:",O)}},C=async()=>{p(!0);try{const O=await fetch(In("/api/user/links"),{headers:{Authorization:`Bearer ${t}`}});if(O.ok){const T=await O.json();v(T.links)}}catch(O){console.error("Error fetching links:",O)}finally{p(!1)}},j=async()=>{if(!x.trim()){i("Please enter a URL");return}p(!0);try{const O=await fetch(In("/api/links/shorten"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({originalUrl:x})}),T=await O.json();O.ok?(i("Link created successfully!"),b(""),C(),k()):i(T.message||"Failed to create link")}catch{i("Failed to create link. Please try again.")}finally{p(!1)}},E=async()=>{const O=[];if((!h.fullName||h.fullName.length<2)&&O.push("Full name must be at least 2 characters"),(!h.address||h.address.length<10)&&O.push("Address must be at least 10 characters"),(!h.phoneNumber||h.phoneNumber.length<10)&&O.push("Phone number must be at least 10 characters"),(!h.telegramUsername||h.telegramUsername.length<1)&&O.push("Telegram username is required"),(!h.trafficSource||h.trafficSource.length<5)&&O.push("Traffic source description must be at least 5 characters"),(!h.trafficSourceLinks||h.trafficSourceLinks.length<10)&&O.push("Traffic source links must be at least 10 characters"),h.paymentMethod||O.push("Please select a payment method"),(!h.paymentDetails||h.paymentDetails.length<5)&&O.push("Payment details must be at least 5 characters"),O.length>0){i(`Validation errors:
• `+O.join(`
• `));return}m(!0);try{const T=await fetch(In("/api/user/payment-details"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(h)}),H=await T.json();if(T.ok)i("Payment details saved successfully! Admin will verify your information."),d(!1),s($=>({...$,fullName:h.fullName,address:h.address,phoneNumber:h.phoneNumber,telegramUsername:h.telegramUsername,trafficSource:h.trafficSource,trafficSourceLinks:h.trafficSourceLinks,paymentMethod:h.paymentMethod,paymentDetails:h.paymentDetails,isPaymentVerified:!1})),k();else if(H.errors){const $=H.errors.map(G=>G.message).join(`
• `);i(`Validation failed:
• `+$)}else i(H.message||"Failed to save payment details")}catch{i("Failed to save payment details. Please try again.")}finally{m(!1)}},L=O=>{navigator.clipboard.writeText(O),i("Link copied to clipboard!"),setTimeout(()=>i(""),3e3)},_=async()=>{if(!r.fullName||!r.paymentMethod){i("Please complete your payment details first");return}if(r.availableBalance<4){i("Minimum withdrawal amount is $4. Current balance: $"+r.availableBalance.toFixed(3));return}const O=prompt(`Enter withdrawal amount (Available: $${r.availableBalance.toFixed(3)}, Minimum: $4):`);if(!O)return;const T=parseFloat(O);if(isNaN(T)||T<4){i("Invalid amount. Minimum withdrawal is $4");return}if(T>r.availableBalance){i("Insufficient balance");return}u(!0);try{const H=await fetch(In("/api/user/withdraw"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({amount:T})}),$=await H.json();H.ok?(i("Withdrawal request submitted successfully! Admin will process your request."),s(G=>({...G,availableBalance:G.availableBalance-T}))):i($.message||"Withdrawal failed")}catch{i("Withdrawal failed. Please try again.")}finally{u(!1)}};return l.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4",children:[l.jsxs("div",{className:"max-w-6xl mx-auto",children:[l.jsx("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20",children:l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsxs("div",{children:[l.jsxs("h1",{className:"text-2xl font-bold text-white mb-2",children:["Welcome back, ",r.username,"! 👋"]}),l.jsx("p",{className:"text-gray-300",children:"Track your earnings and manage your links"})]}),l.jsxs("button",{onClick:n,className:"flex items-center px-4 py-2 bg-red-500/20 text-red-200 rounded-lg hover:bg-red-500/30 transition-colors",children:[l.jsx(O1,{className:"w-4 h-4 mr-2"}),"Logout"]})]})}),r.fullName&&l.jsx("div",{className:`rounded-2xl p-6 mb-6 border ${r.isPaymentVerified?"bg-green-500/20 border-green-500/50":"bg-blue-500/20 border-blue-500/50"}`,children:l.jsxs("div",{className:"flex items-start",children:[r.isPaymentVerified?l.jsx(ar,{className:"w-6 h-6 text-green-400 mr-3 mt-1"}):l.jsx(Hm,{className:"w-6 h-6 text-blue-400 mr-3 mt-1"}),l.jsxs("div",{className:"flex-1",children:[l.jsx("h3",{className:`font-medium mb-2 ${r.isPaymentVerified?"text-green-200":"text-blue-200"}`,children:r.isPaymentVerified?"Payment Details Verified ✅":"Payment Details Under Review"}),l.jsx("p",{className:r.isPaymentVerified?"text-green-300":"text-blue-300",children:r.isPaymentVerified?"Your payment details have been verified. You can now withdraw your earnings!":"Admin is reviewing your payment details. You can withdraw once verified."}),!r.isPaymentVerified&&l.jsx("button",{onClick:()=>d(!0),className:"mt-3 inline-flex items-center px-3 py-1 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm",children:"Update Details"})]})]})}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[l.jsx("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-gray-300 text-sm",children:"Total Earnings"}),l.jsxs("p",{className:"text-2xl font-bold text-green-400",children:["$",((U=r.totalEarnings)==null?void 0:U.toFixed(3))||"0.000"]})]}),l.jsx(Xo,{className:"w-8 h-8 text-green-400"})]})}),l.jsx("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-gray-300 text-sm",children:"Available Balance"}),l.jsxs("p",{className:"text-2xl font-bold text-blue-400",children:["$",((I=r.availableBalance)==null?void 0:I.toFixed(3))||"0.000"]})]}),l.jsx(tf,{className:"w-8 h-8 text-blue-400"})]})}),l.jsx("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-gray-300 text-sm",children:"Total Links"}),l.jsx("p",{className:"text-2xl font-bold text-purple-400",children:r.totalLinks||0})]}),l.jsx(Ga,{className:"w-8 h-8 text-purple-400"})]})}),l.jsx("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("p",{className:"text-gray-300 text-sm",children:"Total Clicks"}),l.jsx("p",{className:"text-2xl font-bold text-pink-400",children:r.totalClicks||0})]}),l.jsx(nf,{className:"w-8 h-8 text-pink-400"})]})})]}),l.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[l.jsxs("button",{onClick:()=>d(!0),className:"inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium",children:[l.jsx(tf,{className:"w-4 h-4 mr-2"}),"Payment"]}),l.jsx("button",{onClick:_,disabled:r.availableBalance<4||!r.fullName||!r.paymentMethod||a,className:`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${r.availableBalance>=4&&r.fullName&&r.paymentMethod&&!a?"bg-green-500 text-white hover:bg-green-600":"bg-gray-500 text-gray-300 cursor-not-allowed"}`,children:a?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):l.jsxs(l.Fragment,{children:[l.jsx(Xo,{className:"w-4 h-4 mr-2"}),"Withdraw",r.availableBalance<4&&l.jsx("span",{className:"ml-1 text-xs",children:"($4 min)"})]})}),l.jsxs("button",{onClick:()=>window.location.href="/",className:"inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors font-medium",children:[l.jsx(_s,{className:"w-4 h-4 mr-2"}),"Homepage"]})]}),o&&l.jsx("div",{className:`rounded-lg p-3 mb-6 ${o.includes("successfully")||o.includes("Success")?"bg-green-500/20 border border-green-500/50 text-green-200":"bg-red-500/20 border border-red-500/50 text-red-200"}`,children:l.jsx("pre",{className:"whitespace-pre-wrap font-sans",children:o})}),l.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 border border-white/20",children:[l.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Create New Link"}),l.jsxs("div",{className:"flex gap-4",children:[l.jsx("input",{type:"url",value:x,onChange:O=>b(O.target.value),className:"flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400",placeholder:"Enter URL to shorten (e.g., https://example.com)"}),l.jsx("button",{onClick:j,disabled:g||!x.trim(),className:"px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium",children:g?"Creating...":"Create Link"})]}),l.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"💰 Earn $0.007 for every click on your shortened links!"})]}),l.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20",children:[l.jsx("h2",{className:"text-xl font-bold text-white mb-4",children:"Your Links"}),g?l.jsxs("div",{className:"text-center py-8",children:[l.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"}),l.jsx("p",{className:"text-gray-300 mt-2",children:"Loading your links..."})]}):y.length===0?l.jsxs("div",{className:"text-center py-8",children:[l.jsx(Ga,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-300",children:"No links created yet"}),l.jsx("p",{className:"text-gray-400 text-sm",children:"Create your first link to start earning!"})]}):l.jsx("div",{className:"space-y-4",children:y.map(O=>l.jsx("div",{className:"bg-white/5 rounded-lg p-4 border border-white/10",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center mb-2",children:[l.jsx("p",{className:"text-white font-medium truncate mr-4",children:O.originalUrl}),l.jsx("span",{className:"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded",children:O.shortCode})]}),l.jsxs("div",{className:"flex items-center text-sm text-gray-300 space-x-4",children:[l.jsxs("span",{className:"flex items-center",children:[l.jsx(nf,{className:"w-4 h-4 mr-1"}),O.clicks," clicks"]}),l.jsxs("span",{className:"flex items-center",children:[l.jsx(Xo,{className:"w-4 h-4 mr-1"}),"$",(O.earnings||0).toFixed(3)]}),l.jsx("span",{children:new Date(O.createdAt).toLocaleDateString()})]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsx("button",{onClick:()=>L(`${window.location.origin}/s/${O.shortCode}`),className:"p-2 bg-blue-500/20 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-colors",title:"Copy link",children:l.jsx(Wm,{className:"w-4 h-4"})}),l.jsx("a",{href:`/s/${O.shortCode}`,target:"_blank",rel:"noopener noreferrer",className:"p-2 bg-purple-500/20 text-purple-300 rounded-lg hover:bg-purple-500/30 transition-colors",title:"Open link",children:l.jsx(_s,{className:"w-4 h-4"})})]})]})},O.id))})]})]}),c&&l.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50",children:l.jsxs("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-6 w-full max-w-2xl border border-white/20 max-h-[90vh] overflow-y-auto",children:[l.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"Payment Details & Verification"}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name * (min 2 characters)"}),l.jsx("input",{type:"text",value:h.fullName,onChange:O=>N(T=>({...T,fullName:O.target.value})),className:`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${h.fullName.length>=2?"border-green-500/50":"border-white/20"}`,placeholder:"Enter your full name (e.g., John Smith)",required:!0}),l.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:[h.fullName.length,"/2+ characters"]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number *"}),l.jsx("input",{type:"tel",value:h.phoneNumber,onChange:O=>N(T=>({...T,phoneNumber:O.target.value})),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400",placeholder:"+1234567890",required:!0})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Complete Address * (min 10 characters)"}),l.jsx("textarea",{value:h.address,onChange:O=>N(T=>({...T,address:O.target.value})),className:`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${h.address.length>=10?"border-green-500/50":"border-white/20"}`,placeholder:"123 Main Street, New York, NY 10001, USA",rows:3,required:!0}),l.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:[h.address.length,"/10+ characters"]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Telegram Username *"}),l.jsx("input",{type:"text",value:h.telegramUsername,onChange:O=>N(T=>({...T,telegramUsername:O.target.value})),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400",placeholder:"@yourusername",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Payment Method *"}),l.jsxs("select",{value:h.paymentMethod,onChange:O=>N(T=>({...T,paymentMethod:O.target.value})),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-400 [&>option]:bg-gray-800 [&>option]:text-white",required:!0,children:[l.jsx("option",{value:"",className:"bg-gray-800 text-white",children:"Select payment method"}),l.jsx("option",{value:"upi",className:"bg-gray-800 text-white",children:"🇮🇳 UPI ID (India)"}),l.jsx("option",{value:"paypal",className:"bg-gray-800 text-white",children:"💳 PayPal"}),l.jsx("option",{value:"bank",className:"bg-gray-800 text-white",children:"🏦 Bank Transfer"}),l.jsx("option",{value:"skrill",className:"bg-gray-800 text-white",children:"💰 Skrill"}),l.jsx("option",{value:"other",className:"bg-gray-800 text-white",children:"🔧 Other (Contact Support)"})]})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Payment Details *"}),h.paymentMethod==="other"?l.jsxs("div",{className:"space-y-3",children:[l.jsx("textarea",{value:h.paymentDetails,onChange:O=>N(T=>({...T,paymentDetails:O.target.value})),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400",placeholder:"Describe your preferred payment method (e.g., Crypto wallet, Mobile money, etc.)",rows:3,required:!0}),l.jsx("div",{className:"bg-blue-500/20 border border-blue-500/50 rounded-lg p-3",children:l.jsxs("p",{className:"text-blue-200 text-sm",children:["📞 ",l.jsx("strong",{children:"Contact Support:"}),' Since you selected "Other", please contact our support team at'," ",l.jsx("span",{className:"font-mono bg-blue-500/30 px-1 rounded",children:"<EMAIL>"})," or"," ",l.jsx("span",{className:"font-mono bg-blue-500/30 px-1 rounded",children:"@SupportBot"})," on Telegram to discuss your payment method."]})})]}):l.jsx("input",{type:"text",value:h.paymentDetails,onChange:O=>N(T=>({...T,paymentDetails:O.target.value})),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400",placeholder:h.paymentMethod==="upi"?"yourname@paytm (e.g., john@paytm, mary@phonepe)":h.paymentMethod==="paypal"?"<EMAIL>":h.paymentMethod==="bank"?"Account Number, Bank Name, IFSC Code":h.paymentMethod==="skrill"?"<EMAIL>":"Enter payment details",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Traffic Source Description * (min 5 characters)"}),l.jsx("textarea",{value:h.trafficSource,onChange:O=>N(T=>({...T,trafficSource:O.target.value})),className:`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${h.trafficSource.length>=5?"border-green-500/50":"border-white/20"}`,placeholder:"Describe where you get traffic for your links (social media, websites, etc.)",rows:2,required:!0}),l.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:[h.trafficSource.length,"/5+ characters"]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Traffic Source Links * (min 10 characters)"}),l.jsx("textarea",{value:h.trafficSourceLinks,onChange:O=>N(T=>({...T,trafficSourceLinks:O.target.value})),className:`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-400 ${h.trafficSourceLinks.length>=10?"border-green-500/50":"border-white/20"}`,placeholder:`Provide links to your channels/websites where you share links:
• YouTube: https://youtube.com/@yourchannel
• Instagram: https://instagram.com/yourpage
• Website: https://yourwebsite.com
• Telegram: https://t.me/yourchannel`,rows:4,required:!0}),l.jsxs("p",{className:"text-xs text-gray-400 mt-1",children:["📝 ",h.trafficSourceLinks.length,"/10+ characters - Provide actual links to verify your traffic sources"]})]}),l.jsxs("div",{className:"bg-blue-500/20 border border-blue-500/50 rounded-lg p-4",children:[l.jsx("h4",{className:"text-blue-200 font-medium mb-2",children:"📋 Verification Process"}),l.jsxs("ul",{className:"text-blue-300 text-sm space-y-1",children:[l.jsx("li",{children:"• Admin will verify your information manually"}),l.jsx("li",{children:"• Ensure all details are accurate and complete"}),l.jsx("li",{children:"• Verification may take 24-48 hours"}),l.jsx("li",{children:"• You can withdraw once verified"}),l.jsx("li",{children:'• For "Other" payment methods, contact support first'})]}),l.jsx("div",{className:"mt-3 pt-3 border-t border-blue-500/30",children:l.jsxs("p",{className:"text-blue-200 text-sm",children:["📞 ",l.jsx("strong",{children:"Need Help?"})," Contact support at"," ",l.jsx("span",{className:"font-mono bg-blue-500/30 px-1 rounded",children:"<EMAIL>"})]})})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx("button",{onClick:()=>d(!1),className:"flex-1 px-4 py-3 bg-gray-500/20 text-gray-300 rounded-lg hover:bg-gray-500/30 transition-colors",children:"Cancel"}),l.jsx("button",{onClick:E,disabled:f||!h.fullName||!h.paymentMethod,className:"flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Saving...":"Save Payment Details"})]})]})]})})]})}function mb({user:e,token:t,onLogin:n,onLogout:r}){return l.jsxs(Wv,{children:[l.jsx(Mn,{path:"/",component:sb}),l.jsx(Mn,{path:"/auth",component:()=>l.jsx(gf,{onLogin:n})}),l.jsx(Mn,{path:"/dashboard",component:()=>e&&t?l.jsx(pb,{user:e,token:t,onLogout:r}):l.jsx(gf,{onLogin:n})}),l.jsx(Mn,{path:"/redirect-step1",component:ob}),l.jsx(Mn,{path:"/redirect-step2",component:ib}),l.jsx(Mn,{path:"/redirect-final",component:ab}),l.jsx(Mn,{component:hb})]})}function gb(){const[e,t]=w.useState(null),[n,r]=w.useState(null);w.useEffect(()=>{const i=localStorage.getItem("token"),a=localStorage.getItem("user");if(i&&a)try{r(i),t(JSON.parse(a))}catch(u){console.error("Error parsing stored user data:",u),localStorage.removeItem("token"),localStorage.removeItem("user")}},[]);const s=(i,a)=>{t(i),r(a)},o=()=>{t(null),r(null),localStorage.removeItem("token"),localStorage.removeItem("user")};return l.jsx(dx,{client:Cx,children:l.jsxs(q2,{children:[l.jsx(Nw,{}),l.jsx(mb,{user:e,token:n,onLogin:s,onLogout:o})]})})}Up(document.getElementById("root")).render(l.jsx(gb,{}));
