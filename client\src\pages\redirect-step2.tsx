import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Clock, ArrowDown, Briefcase, GraduationCap, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function RedirectStep2() {
  const [, setLocation] = useLocation();
  const [countdown, setCountdown] = useState(10);
  const [showContinue, setShowContinue] = useState(false);
  const [secondCountdown, setSecondCountdown] = useState(20);
  const [showSecondContinue, setShowSecondContinue] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);

  // Get short code from URL
  const shortCode = new URLSearchParams(window.location.search).get('code') || '';

  useEffect(() => {
    // Load Monetag MultiTag script
    const multitagScript = document.createElement('script');
    multitagScript.src = 'https://fpyf8.com/88/tag.min.js';
    multitagScript.setAttribute('data-zone', '156349');
    multitagScript.async = true;
    multitagScript.setAttribute('data-cfasync', 'false');
    document.body.appendChild(multitagScript);

    // Load native banner (interstitial) script
    const interstitialScript = document.createElement('script');
    interstitialScript.text = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('groleegni.net',9550747,document.createElement('script'))`;
    document.body.appendChild(interstitialScript);

    return () => {
      // Cleanup scripts on unmount
      if (document.body.contains(multitagScript)) {
        document.body.removeChild(multitagScript);
      }
      if (document.body.contains(interstitialScript)) {
        document.body.removeChild(interstitialScript);
      }
    };
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setShowContinue(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      if (scrollPosition > windowHeight * 0.5) {
        setHasScrolled(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleFirstContinue = () => {
    const timer = setInterval(() => {
      setSecondCountdown(prev => {
        if (prev <= 1) {
          setShowSecondContinue(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setTimeout(() => clearInterval(timer), 20000);
  };

  const handleSecondContinue = () => {
    if (hasScrolled) {
      // Open new tab for final step
      const newTab = window.open(`/redirect-final?code=${shortCode}`, '_blank');
      if (newTab) {
        newTab.focus();
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600">
      
      {/* Navigation */}
      <nav className="glassmorphism relative z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <Briefcase className="text-white text-2xl" />
              <span className="text-white text-xl font-bold">Gaming Career Hub</span>
            </div>
          </div>
        </div>
      </nav>

      {/* Countdown Banner */}
      {countdown > 0 && (
        <div className="bg-blue-600 text-white text-center py-3">
          <div className="flex items-center justify-center space-x-2">
            <Clock className="h-5 w-5" />
            <span className="font-bold">Please wait {countdown} seconds...</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="glassmorphism rounded-xl p-8 mb-8 text-center">
          <h1 className="text-4xl font-bold text-white mb-4">
            🚀 Build Your Gaming Career
          </h1>
          <p className="text-gray-300 text-lg">
            Transform your passion for gaming into a successful career
          </p>
        </div>

        {/* Ad Space */}
        <div className="glassmorphism rounded-xl p-6 mb-8 text-center">
          <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8">
            <p className="text-gray-300">Advertisement Space</p>
          </div>
        </div>

        {/* Gaming Career Articles */}
        <div className="space-y-8">
          <article className="glassmorphism rounded-xl p-8">
            <div className="flex items-center space-x-2 mb-4">
              <GraduationCap className="text-green-400 h-6 w-6" />
              <span className="text-green-400 font-semibold">Career Guide</span>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              How to Start Your Professional Gaming Journey
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Professional Gaming" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Breaking into the professional gaming industry requires dedication, skill development, and strategic planning. Whether you want to become a competitive player, content creator, or work behind the scenes, there are multiple paths to success in the gaming world.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Professional gamers today earn substantial incomes through tournament winnings, sponsorships, streaming revenue, and brand partnerships. The industry has matured to offer stable career opportunities for talented individuals.
            </p>
          </article>

          {/* Continue Button Section */}
          {showContinue && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <Button 
                onClick={handleFirstContinue}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-8 rounded-lg text-lg"
              >
                Learn More About Gaming Careers
              </Button>
            </div>
          )}

          {/* Ad Space */}
          <div className="glassmorphism rounded-xl p-6 text-center">
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8">
              <p className="text-gray-300">Advertisement Space</p>
            </div>
          </div>

          <article className="glassmorphism rounded-xl p-8">
            <div className="flex items-center space-x-2 mb-4">
              <Zap className="text-yellow-400 h-6 w-6" />
              <span className="text-yellow-400 font-semibold">Success Stories</span>
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">
              From Bedroom to Big League: Gaming Success Stories
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1556075798-4825dfaaf498?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Success" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Many of today's top gaming professionals started as casual players who turned their hobby into lucrative careers. These success stories demonstrate that with the right approach, dedication, and timing, anyone can make it in the gaming industry.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Content creation has become one of the most accessible entry points into the gaming industry. Platforms like Twitch, YouTube, and TikTok have created opportunities for gamers to build audiences and monetize their skills and personality.
            </p>
          </article>

          {/* Second Countdown and Continue */}
          {secondCountdown > 0 && showContinue && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Clock className="h-5 w-5 text-blue-400" />
                <span className="text-white font-bold">Please wait {secondCountdown} more seconds...</span>
              </div>
              <div className="animate-bounce">
                <ArrowDown className="h-8 w-8 text-blue-400 mx-auto" />
              </div>
              <p className="text-gray-300 mt-2">Scroll down to continue</p>
            </div>
          )}

          <article className="glassmorphism rounded-xl p-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Skills Every Gaming Professional Needs
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Skills" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-xl font-bold text-white mb-3">Technical Skills</h3>
                <ul className="text-gray-300 space-y-2">
                  <li>• Game mastery and mechanical skill</li>
                  <li>• Understanding of game meta and strategies</li>
                  <li>• Stream setup and technical knowledge</li>
                  <li>• Video editing and content creation</li>
                </ul>
              </div>
              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-xl font-bold text-white mb-3">Soft Skills</h3>
                <ul className="text-gray-300 space-y-2">
                  <li>• Communication and presentation</li>
                  <li>• Marketing and self-promotion</li>
                  <li>• Community management</li>
                  <li>• Business and financial planning</li>
                </ul>
              </div>
            </div>
          </article>

          {/* Ad Space */}
          <div className="glassmorphism rounded-xl p-6 text-center">
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8">
              <p className="text-gray-300">Advertisement Space</p>
            </div>
          </div>

          {showSecondContinue && hasScrolled && (
            <div className="glassmorphism rounded-xl p-8 text-center">
              <Button 
                onClick={handleSecondContinue}
                className="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-8 rounded-lg text-lg"
              >
                Continue to Final Step
              </Button>
            </div>
          )}

          <article className="glassmorphism rounded-xl p-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Building Your Gaming Brand
            </h2>
            <img 
              src="https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
              alt="Gaming Brand" 
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            <p className="text-gray-300 text-lg leading-relaxed mb-6">
              Personal branding is crucial in the gaming industry. Your brand represents your unique personality, gaming style, and the value you provide to your audience. Successful gaming professionals invest time in developing a consistent and authentic brand identity.
            </p>
            <p className="text-gray-300 text-lg leading-relaxed">
              Networking within the gaming community, collaborating with other creators, and staying up-to-date with industry trends are essential for long-term success in this rapidly evolving field.
            </p>
          </article>

          {/* Ad Space */}
          <div className="glassmorphism rounded-xl p-6 text-center">
            <div className="bg-gray-700 bg-opacity-50 rounded-lg p-8">
              <p className="text-gray-300">Advertisement Space</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}