// Test the unlocked withdraw functionality
async function testUnlockedWithdraw() {
  try {
    console.log('🧪 Testing Unlocked Withdraw Functionality...\n');
    
    // Test 1: Create user WITHOUT payment details
    console.log('1. Testing user without payment details...');
    const username1 = 'nodetails' + Date.now();
    const registerResponse1 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: username1, password: 'testpass123' })
    });
    
    const registerData1 = await registerResponse1.json();
    console.log('✅ User without payment details created:', registerData1.message);
    const token1 = registerData1.token;
    
    // Set balance for withdrawal testing
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token1}`
      },
      body: JSON.stringify({ balance: 8.50 })
    });
    console.log('✅ Balance set to $8.50');
    
    // Test withdrawal WITHOUT payment details
    console.log('\n2. Testing withdrawal WITHOUT payment details...');
    const withdrawResponse1 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token1}`
      },
      body: JSON.stringify({ amount: 4.00 })
    });
    
    const withdrawData1 = await withdrawResponse1.json();
    console.log('✅ Withdrawal without payment details:', withdrawData1.message);
    
    // Test 2: Create user WITH payment details
    console.log('\n3. Testing user with payment details...');
    const username2 = 'withdetails' + Date.now();
    const registerResponse2 = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: username2, password: 'testpass123' })
    });
    
    const registerData2 = await registerResponse2.json();
    console.log('✅ User with payment details created:', registerData2.message);
    const token2 = registerData2.token;
    
    // Add payment details
    await fetch('http://localhost:3000/api/user/payment-details', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token2}`
      },
      body: JSON.stringify({
        fullName: 'Complete User Test',
        address: '123 Complete Address Street, Test City, TC 12345, USA',
        phoneNumber: '+1234567890',
        telegramUsername: '@completetest',
        trafficSource: 'Testing complete user withdrawal flow',
        trafficSourceLinks: '• YouTube: https://youtube.com/@completetest\n• Website: https://completetest.com',
        paymentMethod: 'paypal',
        paymentDetails: '<EMAIL>'
      })
    });
    console.log('✅ Payment details added');
    
    // Set balance
    await fetch('http://localhost:3000/api/test/set-balance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token2}`
      },
      body: JSON.stringify({ balance: 12.75 })
    });
    console.log('✅ Balance set to $12.75');
    
    // Test withdrawal WITH payment details
    console.log('\n4. Testing withdrawal WITH payment details...');
    const withdrawResponse2 = await fetch('http://localhost:3000/api/user/withdraw', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token2}`
      },
      body: JSON.stringify({ amount: 6.50 })
    });
    
    const withdrawData2 = await withdrawResponse2.json();
    console.log('✅ Withdrawal with payment details:', withdrawData2.message);
    
    console.log('\n🎉 All withdraw tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`• User 1 (${username1}): No payment details → Withdrawal allowed`);
    console.log(`• User 2 (${username2}): Complete payment details → Withdrawal allowed`);
    console.log('\n📱 Telegram Posts Created:');
    console.log('• Post 1: ⚠️ INCOMPLETE DETAILS - Admin needs to contact user');
    console.log('• Post 2: ✅ COMPLETE DETAILS - Admin can process immediately');
    console.log('\n🔧 Admin Benefits:');
    console.log('• Clear status indicators in Telegram posts');
    console.log('• Verification checklist for each withdrawal');
    console.log('• Auto-pinned posts for visibility');
    console.log('• Balance verification included');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testUnlockedWithdraw();
