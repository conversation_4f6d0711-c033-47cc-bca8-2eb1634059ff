// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/storage.ts
import TelegramBot from "node-telegram-bot-api";
import bcrypt from "bcryptjs";
var TelegramStorage = class {
  bot;
  channelId;
  cache = /* @__PURE__ */ new Map();
  userCache = /* @__PURE__ */ new Map();
  usernameCache = /* @__PURE__ */ new Map();
  withdrawalCache = /* @__PURE__ */ new Map();
  initialized = false;
  // Earnings configuration
  EARNINGS_PER_CLICK = 7e-3;
  // $0.007 per click
  MIN_WITHDRAWAL = 4;
  // $4 minimum withdrawal
  constructor() {
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const channelId = process.env.TELEGRAM_CHANNEL_ID;
    if (!botToken || !channelId) {
      throw new Error("TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID must be set");
    }
    this.bot = new TelegramBot(botToken);
    this.channelId = channelId;
  }
  async initializeCache() {
    if (this.initialized) return;
    try {
      await this.loadLinksFromChannel();
      this.initialized = true;
    } catch (error) {
      console.error("Failed to initialize Telegram storage cache:", error);
      this.initialized = true;
    }
  }
  async loadLinksFromChannel() {
    try {
      console.log("Loading existing links from Telegram channel...");
      console.log("Cache will be built as links are accessed or created.");
    } catch (error) {
      console.error("Error loading links from channel:", error);
    }
  }
  async createShortenedLink(linkData) {
    await this.initializeCache();
    const link = {
      id: Date.now(),
      // Use timestamp as ID
      originalUrl: linkData.originalUrl,
      shortCode: linkData.shortCode,
      clicks: 0,
      createdAt: /* @__PURE__ */ new Date(),
      userId: linkData.userId,
      earnings: 0
    };
    try {
      const message = JSON.stringify({
        type: "shortened_link",
        data: link
      });
      await this.bot.sendMessage(this.channelId, message);
      this.cache.set(linkData.shortCode, link);
      console.log(`Saved link to Telegram: ${linkData.shortCode} -> ${linkData.originalUrl}`);
      return link;
    } catch (error) {
      console.error("Error saving link to Telegram:", error);
      throw new Error("Failed to save shortened link");
    }
  }
  async getShortenedLink(shortCode) {
    await this.initializeCache();
    const cachedLink = this.cache.get(shortCode);
    if (cachedLink) {
      return cachedLink;
    }
    try {
      console.log(`Searching Telegram history for shortCode: ${shortCode}`);
      let offset = 0;
      const limit = 50;
      const maxSearchMessages = 200;
      while (offset < maxSearchMessages) {
        try {
          const chatId = parseInt(this.channelId);
          console.log(`Cannot search Telegram history automatically. Link ${shortCode} exists in channel but requires manual recovery.`);
          console.log(`Check your Telegram channel ${this.channelId} for the link data.`);
          return void 0;
        } catch (searchError) {
          console.error("Error in Telegram search:", searchError);
          break;
        }
      }
      return void 0;
    } catch (error) {
      console.error("Error searching Telegram history:", error);
      return void 0;
    }
  }
  async updateLinkClicks(shortCode, userId) {
    await this.initializeCache();
    const link = this.cache.get(shortCode);
    if (link) {
      link.clicks += 1;
      if (link.userId && userId) {
        const earnings = this.EARNINGS_PER_CLICK;
        link.earnings = (link.earnings || 0) + earnings;
        await this.updateUserEarnings(link.userId, earnings);
      }
      try {
        const message = JSON.stringify({
          type: "click_update",
          shortCode,
          clicks: link.clicks,
          earnings: link.earnings || 0,
          userId: link.userId,
          timestamp: (/* @__PURE__ */ new Date()).toISOString()
        });
        await this.bot.sendMessage(this.channelId, message);
      } catch (error) {
        console.error("Error updating clicks in Telegram:", error);
      }
    }
  }
  async getRecentLinks() {
    await this.initializeCache();
    const links = Array.from(this.cache.values()).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime()).slice(0, 10);
    return links;
  }
  async getUserLinks(userId) {
    await this.initializeCache();
    const links = Array.from(this.cache.values()).filter((link) => link.userId === userId).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    return links;
  }
  // User Management Methods
  async createUser(userData) {
    await this.initializeCache();
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    const user = {
      id: Date.now(),
      username: userData.username,
      password: hashedPassword,
      totalEarnings: 0,
      availableBalance: 0,
      totalClicks: 0,
      totalLinks: 0,
      createdAt: /* @__PURE__ */ new Date(),
      isActive: true
    };
    try {
      const message = JSON.stringify({
        type: "user_created",
        data: user
      });
      await this.bot.sendMessage(this.channelId, message);
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);
      console.log(`Created user: ${user.username} (ID: ${user.id})`);
      return user;
    } catch (error) {
      console.error("Error creating user in Telegram:", error);
      throw new Error("Failed to create user");
    }
  }
  async getUserByUsername(username) {
    await this.initializeCache();
    return this.usernameCache.get(username);
  }
  async getUserById(id) {
    await this.initializeCache();
    return this.userCache.get(id);
  }
  async updateUserPaymentDetails(userId, paymentData) {
    await this.initializeCache();
    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error("User not found");
    }
    user.fullName = paymentData.fullName;
    user.address = paymentData.address;
    user.phoneNumber = paymentData.phoneNumber;
    user.telegramUsername = paymentData.telegramUsername;
    user.trafficSource = paymentData.trafficSource;
    user.trafficSourceLinks = paymentData.trafficSourceLinks;
    user.paymentMethod = paymentData.paymentMethod;
    user.paymentDetails = paymentData.paymentDetails;
    user.isPaymentVerified = false;
    try {
      const message = JSON.stringify({
        type: "payment_details_updated",
        userId,
        username: user.username,
        paymentData: {
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          needsSupport: user.paymentMethod === "other" ? true : false
        },
        adminNote: user.paymentMethod === "other" ? '\u26A0\uFE0F USER SELECTED "OTHER" PAYMENT METHOD - CONTACT SUPPORT REQUIRED' : "Standard payment method selected",
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      }, null, 2);
      await this.bot.sendMessage(this.channelId, message);
      this.userCache.set(user.id, user);
      this.usernameCache.set(user.username, user);
      console.log(`Updated payment details for user ${user.username}`);
      return user;
    } catch (error) {
      console.error("Error updating payment details in Telegram:", error);
      throw new Error("Failed to update payment details");
    }
  }
  async updateUserEarnings(userId, amount) {
    await this.initializeCache();
    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for earnings update`);
      return;
    }
    user.totalEarnings += amount;
    user.availableBalance += amount;
    user.totalClicks += 1;
  }
  // Temporary method for testing - manually set user balance
  async setUserBalanceForTesting(userId, balance) {
    await this.initializeCache();
    const user = this.userCache.get(userId);
    if (!user) {
      console.error(`User ${userId} not found for balance update`);
      return;
    }
    user.availableBalance = balance;
    user.totalEarnings = balance;
    console.log(`\u{1F9EA} TEST: Set balance for user ${user.username} to $${balance.toFixed(3)}`);
  }
  async createWithdrawalRequest(userId, amount, method) {
    await this.initializeCache();
    const user = this.userCache.get(userId);
    if (!user) {
      throw new Error("User not found");
    }
    if (user.availableBalance < amount) {
      throw new Error("Insufficient balance");
    }
    if (amount < this.MIN_WITHDRAWAL) {
      throw new Error(`Minimum withdrawal amount is $${this.MIN_WITHDRAWAL}`);
    }
    const withdrawal = {
      id: Date.now(),
      userId,
      amount,
      method,
      status: "pending",
      requestedAt: /* @__PURE__ */ new Date()
    };
    try {
      const message = JSON.stringify({
        type: "withdrawal_request",
        withdrawalId: withdrawal.id,
        amount: withdrawal.amount,
        method: withdrawal.method,
        status: withdrawal.status,
        requestedAt: withdrawal.requestedAt,
        user: {
          id: user.id,
          username: user.username,
          fullName: user.fullName,
          address: user.address,
          phoneNumber: user.phoneNumber,
          telegramUsername: user.telegramUsername,
          trafficSource: user.trafficSource,
          trafficSourceLinks: user.trafficSourceLinks,
          paymentMethod: user.paymentMethod,
          paymentDetails: user.paymentDetails,
          totalEarnings: user.totalEarnings,
          availableBalance: user.availableBalance,
          totalClicks: user.totalClicks,
          totalLinks: user.totalLinks,
          isPaymentVerified: user.isPaymentVerified
        }
      }, null, 2);
      await this.bot.sendMessage(this.channelId, message);
      user.availableBalance -= amount;
      this.userCache.set(user.id, user);
      const userWithdrawals = this.withdrawalCache.get(userId) || [];
      userWithdrawals.push(withdrawal);
      this.withdrawalCache.set(userId, userWithdrawals);
      console.log(`Withdrawal request created: $${amount} for user ${user.username}`);
      return withdrawal;
    } catch (error) {
      console.error("Error creating withdrawal request:", error);
      throw new Error("Failed to create withdrawal request");
    }
  }
  async getUserWithdrawals(userId) {
    await this.initializeCache();
    return this.withdrawalCache.get(userId) || [];
  }
};
var _storage = null;
var storage = {
  get instance() {
    if (!_storage) {
      _storage = new TelegramStorage();
    }
    return _storage;
  },
  // Proxy all link methods to the instance
  async createShortenedLink(link) {
    return this.instance.createShortenedLink(link);
  },
  async getShortenedLink(shortCode) {
    return this.instance.getShortenedLink(shortCode);
  },
  async updateLinkClicks(shortCode, userId) {
    return this.instance.updateLinkClicks(shortCode, userId);
  },
  async getRecentLinks() {
    return this.instance.getRecentLinks();
  },
  async getUserLinks(userId) {
    return this.instance.getUserLinks(userId);
  },
  // Proxy all user methods to the instance
  async createUser(userData) {
    return this.instance.createUser(userData);
  },
  async getUserByUsername(username) {
    return this.instance.getUserByUsername(username);
  },
  async getUserById(id) {
    return this.instance.getUserById(id);
  },
  async updateUserPaymentDetails(userId, paymentData) {
    return this.instance.updateUserPaymentDetails(userId, paymentData);
  },
  async updateUserEarnings(userId, amount) {
    return this.instance.updateUserEarnings(userId, amount);
  },
  // Proxy all withdrawal methods to the instance
  async createWithdrawalRequest(userId, amount, method) {
    return this.instance.createWithdrawalRequest(userId, amount, method);
  },
  async getUserWithdrawals(userId) {
    return this.instance.getUserWithdrawals(userId);
  },
  // Temporary testing method
  async setUserBalanceForTesting(userId, balance) {
    return this.instance.setUserBalanceForTesting(userId, balance);
  }
};

// shared/schema.ts
import { z } from "zod";
var insertLinkSchema = z.object({
  originalUrl: z.string().url("Invalid URL format")
});
var insertUserSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  password: z.string().min(6, "Password must be at least 6 characters")
});
var loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required")
});
var telegramRegistrationSchema = z.object({
  telegramId: z.string(),
  mobileNumber: z.string().min(10, "Valid mobile number required"),
  withdrawalMethod: z.string().min(1, "Withdrawal method required")
});
var paymentDetailsSchema = z.object({
  fullName: z.string().min(2, "Full name is required"),
  address: z.string().min(10, "Complete address is required"),
  phoneNumber: z.string().min(10, "Valid phone number is required"),
  telegramUsername: z.string().min(1, "Telegram username is required"),
  trafficSource: z.string().min(5, "Please describe your traffic source"),
  trafficSourceLinks: z.string().min(10, "Please provide links to your traffic sources"),
  paymentMethod: z.enum(["upi", "paypal", "bank", "skrill", "other"], {
    required_error: "Please select a payment method"
  }),
  paymentDetails: z.string().min(5, "Payment details are required")
});
var withdrawalRequestSchema = z.object({
  amount: z.number().min(4, "Minimum withdrawal amount is $4")
});

// server/routes.ts
import { nanoid } from "nanoid";

// server/auth.ts
import jwt from "jsonwebtoken";
import bcrypt2 from "bcryptjs";
var JWT_SECRET = process.env.JWT_SECRET || "your-secret-key-change-in-production";
function generateToken(user) {
  return jwt.sign(
    { id: user.id, username: user.username },
    JWT_SECRET,
    { expiresIn: "7d" }
  );
}
function authenticateToken(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];
  if (!token) {
    return res.status(401).json({ message: "Access token required" });
  }
  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(403).json({ message: "Invalid or expired token" });
    }
    req.user = decoded;
    next();
  });
}
function optionalAuth(req, res, next) {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];
  if (token) {
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (!err) {
        req.user = decoded;
      }
    });
  }
  next();
}
async function register(req, res) {
  try {
    const validation = insertUserSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        message: "Validation failed",
        errors: validation.error.errors
      });
    }
    const { username, password } = validation.data;
    const existingUser = await storage.getUserByUsername(username);
    if (existingUser) {
      return res.status(409).json({ message: "Username already exists" });
    }
    const user = await storage.createUser({ username, password });
    const token = generateToken({ id: user.id, username: user.username });
    res.status(201).json({
      message: "User created successfully",
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        createdAt: user.createdAt
      },
      token
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({ message: "Failed to create user" });
  }
}
async function login(req, res) {
  try {
    const validation = loginSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        message: "Validation failed",
        errors: validation.error.errors
      });
    }
    const { username, password } = validation.data;
    const user = await storage.getUserByUsername(username);
    if (!user) {
      return res.status(401).json({ message: "Invalid credentials" });
    }
    const isValidPassword = await bcrypt2.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: "Invalid credentials" });
    }
    const token = generateToken({ id: user.id, username: user.username });
    res.json({
      message: "Login successful",
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        fullName: user.fullName,
        address: user.address,
        phoneNumber: user.phoneNumber,
        telegramUsername: user.telegramUsername,
        trafficSource: user.trafficSource,
        trafficSourceLinks: user.trafficSourceLinks,
        paymentMethod: user.paymentMethod,
        paymentDetails: user.paymentDetails,
        isPaymentVerified: user.isPaymentVerified,
        createdAt: user.createdAt
      },
      token
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Login failed" });
  }
}
async function getProfile(req, res) {
  try {
    if (!req.user) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    const user = await storage.getUserById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    res.json({
      user: {
        id: user.id,
        username: user.username,
        totalEarnings: user.totalEarnings,
        availableBalance: user.availableBalance,
        totalClicks: user.totalClicks,
        totalLinks: user.totalLinks,
        fullName: user.fullName,
        address: user.address,
        phoneNumber: user.phoneNumber,
        telegramUsername: user.telegramUsername,
        trafficSource: user.trafficSource,
        trafficSourceLinks: user.trafficSourceLinks,
        paymentMethod: user.paymentMethod,
        paymentDetails: user.paymentDetails,
        isPaymentVerified: user.isPaymentVerified,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error("Profile error:", error);
    res.status(500).json({ message: "Failed to get profile" });
  }
}

// server/routes.ts
function generateShortCode() {
  return nanoid(8);
}
function getNewsFromEnv() {
  const news = [];
  let index = 1;
  while (process.env[`NEWS_${index}`]) {
    try {
      const newsData = JSON.parse(process.env[`NEWS_${index}`] || "{}");
      news.push({
        id: `news_${index}`,
        title: newsData.title || `Gaming News ${index}`,
        excerpt: newsData.excerpt || "Latest gaming news update",
        category: newsData.category || "Gaming",
        date: newsData.date || "Today",
        readTime: newsData.readTime || "3 min read",
        imageUrl: newsData.imageUrl || "https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400"
      });
      index++;
    } catch (e) {
      break;
    }
  }
  return news;
}
function getDealsFromEnv() {
  const deals = [];
  let index = 1;
  while (process.env[`DEAL_${index}`]) {
    try {
      const dealData = JSON.parse(process.env[`DEAL_${index}`] || "{}");
      deals.push({
        id: `deal_${index}`,
        title: dealData.title || `Gaming Deal ${index}`,
        currentPrice: dealData.currentPrice || "$99.99",
        originalPrice: dealData.originalPrice || "$149.99",
        discount: dealData.discount || "33% OFF",
        timeLeft: dealData.timeLeft || "2 days left",
        imageUrl: dealData.imageUrl || "https://images.unsplash.com/photo-1616763355548-1b606f439f86?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300"
      });
      index++;
    } catch (e) {
      break;
    }
  }
  return deals;
}
async function registerRoutes(app2) {
  app2.get("/api/storage/test", async (req, res) => {
    try {
      const botToken = process.env.TELEGRAM_BOT_TOKEN;
      const channelId = process.env.TELEGRAM_CHANNEL_ID;
      if (!botToken || !channelId) {
        return res.status(500).json({
          status: "error",
          message: "Telegram bot not configured. Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHANNEL_ID"
        });
      }
      res.json({
        status: "configured",
        message: "Telegram storage is configured and ready",
        botConfigured: !!botToken,
        channelConfigured: !!channelId
      });
    } catch (error) {
      console.error("Telegram storage test error:", error);
      res.status(500).json({
        status: "error",
        message: "Telegram storage test failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });
  app2.post("/api/auth/register", register);
  app2.post("/api/auth/login", login);
  app2.get("/api/auth/profile", authenticateToken, getProfile);
  app2.get("/api/user/links", authenticateToken, async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const links = await storage.getUserLinks(req.user.id);
      res.json({ links });
    } catch (error) {
      console.error("Error fetching user links:", error);
      res.status(500).json({ message: "Failed to fetch links" });
    }
  });
  app2.get("/api/user/withdrawals", authenticateToken, async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const withdrawals = await storage.getUserWithdrawals(req.user.id);
      res.json({ withdrawals });
    } catch (error) {
      console.error("Error fetching withdrawals:", error);
      res.status(500).json({ message: "Failed to fetch withdrawals" });
    }
  });
  app2.post("/api/user/payment-details", authenticateToken, async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const validation = paymentDetailsSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          message: "Validation failed",
          errors: validation.error.errors
        });
      }
      const paymentData = validation.data;
      await storage.updateUserPaymentDetails(req.user.id, paymentData);
      res.json({
        message: "Payment details saved successfully"
      });
    } catch (error) {
      console.error("Error saving payment details:", error);
      res.status(500).json({
        message: error instanceof Error ? error.message : "Failed to save payment details"
      });
    }
  });
  app2.post("/api/user/withdraw", authenticateToken, async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const validation = withdrawalRequestSchema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({
          message: "Validation failed",
          errors: validation.error.errors
        });
      }
      const { amount } = validation.data;
      const user = await storage.getUserById(req.user.id);
      if (!user?.fullName || !user?.paymentMethod) {
        return res.status(400).json({
          message: "Please complete your payment details before withdrawing"
        });
      }
      const withdrawal = await storage.createWithdrawalRequest(req.user.id, amount, user.paymentMethod);
      res.json({
        message: "Withdrawal request submitted successfully",
        withdrawal
      });
    } catch (error) {
      console.error("Error creating withdrawal:", error);
      res.status(500).json({
        message: error instanceof Error ? error.message : "Failed to create withdrawal request"
      });
    }
  });
  app2.post("/api/test/set-balance", authenticateToken, async (req, res) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      const { balance } = req.body;
      if (typeof balance !== "number" || balance < 0) {
        return res.status(400).json({ message: "Invalid balance amount" });
      }
      await storage.setUserBalanceForTesting(req.user.id, balance);
      res.json({ message: `Balance set to $${balance.toFixed(3)} for testing` });
    } catch (error) {
      console.error("Error setting test balance:", error);
      res.status(500).json({ message: "Failed to set balance" });
    }
  });
  app2.post("/api/links/recover", async (req, res) => {
    try {
      const { shortCode, originalUrl } = req.body;
      if (!shortCode || !originalUrl) {
        return res.status(400).json({
          message: "Missing required fields: shortCode and originalUrl"
        });
      }
      const link = {
        id: Date.now(),
        originalUrl,
        shortCode,
        clicks: 0,
        createdAt: /* @__PURE__ */ new Date()
      };
      const storageInstance = storage.instance;
      storageInstance.cache.set(shortCode, link);
      console.log(`Manually recovered link: ${shortCode} -> ${originalUrl}`);
      res.json({
        message: "Link recovered successfully",
        link,
        shortUrl: `${req.protocol}://${req.get("host")}/s/${shortCode}`
      });
    } catch (error) {
      console.error("Error recovering link:", error);
      res.status(500).json({ message: "Failed to recover link" });
    }
  });
  app2.get("/api/news", async (req, res) => {
    try {
      const news = getNewsFromEnv();
      res.json(news);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch news" });
    }
  });
  app2.get("/api/deals", async (req, res) => {
    try {
      const deals = getDealsFromEnv();
      res.json(deals);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch deals" });
    }
  });
  app2.post("/api/links/shorten", optionalAuth, async (req, res) => {
    try {
      if (process.env.NODE_ENV === "production") {
        console.log("Production - Content-Type:", req.get("Content-Type"));
        console.log("Production - Body type:", typeof req.body);
        console.log("Production - Body:", JSON.stringify(req.body));
      }
      let originalUrl;
      if (req.body && typeof req.body === "object" && req.body.originalUrl) {
        originalUrl = req.body.originalUrl;
      } else if (typeof req.body === "string") {
        try {
          const parsed = JSON.parse(req.body);
          originalUrl = parsed.originalUrl;
        } catch (e) {
          return res.status(400).json({ message: "Invalid JSON format" });
        }
      } else {
        return res.status(400).json({
          message: "originalUrl is required",
          receivedBody: req.body,
          bodyType: typeof req.body
        });
      }
      if (!originalUrl || typeof originalUrl !== "string") {
        return res.status(400).json({
          message: "originalUrl must be a non-empty string",
          received: originalUrl
        });
      }
      const { originalUrl: validatedUrl } = insertLinkSchema.parse({ originalUrl });
      try {
        new URL(validatedUrl);
      } catch (e) {
        return res.status(400).json({ message: "Invalid URL format" });
      }
      const shortCode = generateShortCode();
      const userId = req.user?.id;
      const link = await storage.createShortenedLink({
        originalUrl: validatedUrl,
        shortCode,
        userId
      });
      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get("host")}`;
      if (userId) {
        const user = await storage.getUserById(userId);
        if (user) {
          user.totalLinks += 1;
          console.log(`User ${user.username} created link: ${shortCode}`);
        }
      }
      res.json({
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt,
        isUserLink: !!userId,
        earnings: link.earnings || 0
      });
    } catch (error) {
      console.error("Error in /api/links/shorten:", error);
      if (error instanceof Error && (error.message.includes("TELEGRAM") || error.message.includes("bot"))) {
        return res.status(503).json({
          message: "Telegram storage failed. Please check bot configuration.",
          details: "Unable to save to Telegram. Ensure TELEGRAM_BOT_TOKEN and TELEGRAM_CHANNEL_ID are correct."
        });
      }
      if (error instanceof Error) {
        return res.status(400).json({
          message: "Validation failed",
          details: error.message,
          stack: process.env.NODE_ENV === "development" ? error.stack : void 0
        });
      }
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/s/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }
      res.redirect(`/redirect-step1?code=${shortCode}`);
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/links/original/:shortCode", async (req, res) => {
    try {
      const { shortCode } = req.params;
      const link = await storage.getShortenedLink(shortCode);
      if (!link) {
        return res.status(404).json({ message: "Link not found" });
      }
      await storage.updateLinkClicks(shortCode, link.userId);
      res.json({
        originalUrl: link.originalUrl,
        shortCode: link.shortCode,
        clicks: link.clicks + 1
      });
    } catch (error) {
      res.status(500).json({ message: "Internal server error" });
    }
  });
  app2.get("/api/links/recent", async (req, res) => {
    try {
      const links = await storage.getRecentLinks();
      const baseUrl = process.env.BASE_URL || `${req.protocol}://${req.get("host")}`;
      const formattedLinks = links.map((link) => ({
        id: link.id,
        originalUrl: link.originalUrl,
        shortUrl: `${baseUrl}/s/${link.shortCode}`,
        shortCode: link.shortCode,
        clicks: link.clicks,
        createdAt: link.createdAt
      }));
      res.json(formattedLinks);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch recent links" });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
var vite_config_default = defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid as nanoid2 } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid2()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
import dotenv from "dotenv";
dotenv.config();
console.log("\u{1F4F1} Telegram bot registration disabled - using website dashboard for all user management");
var app = express2();
app.use(express2.json({
  limit: "10mb"
}));
app.use(express2.urlencoded({ extended: true }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = parseInt(process.env.PORT || "5000", 10);
  const host = process.env.NODE_ENV === "development" ? "localhost" : "0.0.0.0";
  server.listen({
    port,
    host,
    reusePort: process.env.NODE_ENV !== "development"
  }, () => {
    log(`serving on ${host}:${port}`);
  });
})();
