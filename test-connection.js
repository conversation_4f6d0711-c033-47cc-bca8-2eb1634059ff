// Test if we can connect to the server
import http from 'http';

function testConnection() {
  console.log('🔍 Testing connection to localhost:5000...\n');
  
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    console.log('✅ Connection successful!');
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('\n📄 Response received (first 200 chars):');
      console.log(data.substring(0, 200) + '...');
      console.log('\n🎉 Server is working correctly!');
    });
  });

  req.on('error', (err) => {
    console.log('❌ Connection failed:', err.message);
    console.log('\n🔧 Troubleshooting suggestions:');
    console.log('1. Check if server is running');
    console.log('2. Check Windows Firewall settings');
    console.log('3. Check antivirus software');
    console.log('4. Try running as administrator');
  });

  req.on('timeout', () => {
    console.log('⏰ Connection timed out');
    req.destroy();
  });

  req.end();
}

testConnection();
