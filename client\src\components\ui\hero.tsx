import { Flame, Newspaper, Trophy, Medal, Star } from "lucide-react";

export default function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative overflow-hidden py-20 px-4">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
          Your Ultimate <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-400 to-white">Gaming Hub</span>
        </h1>
        <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed">
          Stay updated with the latest gaming news, discover amazing deals on gaming products, and shorten your links with our powerful tools.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button 
            onClick={() => scrollToSection('deals')}
            className="bg-pink-500 hover:bg-pink-600 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 hover:scale-105 shadow-lg flex items-center justify-center"
          >
            <Flame className="mr-2 h-5 w-5" />
            View Hot Deals
          </button>
          <button 
            onClick={() => scrollToSection('news')}
            className="glassmorphism text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center"
          >
            <Newspaper className="mr-2 h-5 w-5" />
            Latest News
          </button>
        </div>
      </div>
      
      {/* Floating Gaming Icons */}
      <div className="absolute top-20 left-10 animate-bounce-slow">
        <Trophy className="text-yellow-400 text-3xl opacity-30 h-8 w-8" />
      </div>
      <div className="absolute top-40 right-20 animate-pulse-glow">
        <Medal className="text-pink-400 text-2xl opacity-40 h-6 w-6" />
      </div>
      <div className="absolute bottom-20 left-20 animate-bounce-slow" style={{animationDelay: '1s'}}>
        <Star className="text-yellow-300 text-xl opacity-30 h-5 w-5" />
      </div>
    </section>
  );
}
